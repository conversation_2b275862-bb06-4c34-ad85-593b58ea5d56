import{u as ee,W as te}from"./table-C0LB8EJD.js";import{O as ae}from"./index-ChYhAQZ_.js";import{W as le}from"./index-ZdxrKWAB.js";import{C as re,cu as se,cv as ne,cw as oe,cx as ie,cy as ue,cz as de,cA as ce,r as p,u as pe,y as me,w as he,j as fe,f as k,d as w,o as V,c as B,b as C,g as G,m,aR as O}from"./index-DLKlSkMo.js";import{a as N,S as be,d as q,f as ve}from"./tools-B0tc7tWY.js";import{u as ye}from"./devTree-BwbHtyaz.js";import{B as ge}from"./index-bX4hVoaz.js";import{M as De}from"./index-BJVFa-Ub.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./ActionButton-DDquCBiw.js";import"./styleChecker-tOiS7TSB.js";import"./initDefaultProps-DBdyDbXv.js";import"./shallowequal-C1uyuyZ_.js";import"./index-W_N8ii3N.js";import"./index-DzeUeE5s.js";import"./index-C5qlGDhC.js";import"./index-pJrpCCGr.js";import"./index-Big_Av0Y.js";/* empty css                                                              */const we=re("alarmDefinition",{state:()=>({alarmDefineList:[],measLocations:[],eigenValueTypeList:[]}),actions:{reset(){this.$reset()},async fetchGetAlarmDefineList(u){try{const t=await ce(u);return t&&t.length>0&&(this.alarmDefineList=t),t}catch(t){throw console.error("获取失败:",t),t}},async fetchMeasLocations(u){try{const t=await de(u);let h=N(t,{label:"measLocName",value:"measLocationID"});return this.deviceOptions=h,h}catch(t){throw console.error("获取失败:",t),t}},async fetchGetEigenValueTypeList(u){try{const t=await ue(u);let h=N(t,{label:"eigenValueName",value:"eigenValueID"},{nother:!0});return this.eigenValueTypeList=h,h}catch(t){throw console.error("获取失败:",t),t}},async fetchWarnBatchAddWarnRulee(u){try{return await ie(u)}catch(t){throw console.error(t),t}},async fetchEditWarnRule(u){try{return await oe(u)}catch(t){throw console.error(t),t}},async fetchBatchDeleteWarnRule(u){try{return await ne(u)}catch(t){throw console.error(t),t}},async fetchInitTurbineWarn(u){try{return await se(u)}catch(t){throw console.error(t),t}}}}),Ve={key:2},$e={__name:"alarmDefinition",setup(u){const t=we(),h=ye(),$=ee(),E=async(e,r)=>{let l=null,a="warnValue",s="alarmValue";if(d.value==="edit")l=b.value.getFieldsValue();else{l=v.value.getTableFieldsValue();let D=e.field.match(/\d+/)[0];a=`warnValue[${D}]`,s=`alarmValue[${D}]`}if(!l)return;const n=parseFloat(l[a]),i=parseFloat(l[s]);if(n!==void 0&&i!==void 0&&n>i)return Promise.reject(new Error("正向危险值不能小于正向注意值"));let g=e.field==a?s:a;return d.value==="edit"?b.value.clearValidate(g):v.value.clearValidate(g),Promise.resolve()},F=async(e,r)=>{let l=null,a="reverseWarnValue",s="reverseAlarmValue";if(d.value==="edit")l=b.value.getFieldsValue();else{l=v.value.getTableFieldsValue();let D=e.field.match(/\d+/)[0];a=`reverseWarnValue[${D}]`,s=`reverseAlarmValue[${D}]`}if(!l)return;const n=parseFloat(l[a]),i=parseFloat(l[s]);if(n!==void 0&&i!==void 0&&n<i)return Promise.reject(new Error("反向危险值不能大于反向注意值"));let g=e.field==a?s:a;return d.value==="edit"?b.value.clearValidate(g):v.value.clearValidate(g),Promise.resolve()},I=(e={isform:!1,idEdit:!1})=>{let r=320;return[{title:"测量位置",dataIndex:"measLocationID",columnWidth:150,formItemWidth:r,labelInValue:!e.idEdit,isrequired:!e.idEdit,inputType:"select",selectOptions:[],hasChangeEvent:!0,isdisplay:!e.idEdit,...e.isform?{}:{customRender:({text:l,record:a})=>a.measLocationName}},{title:"特征值",dataIndex:"eigenValueID",columnWidth:160,formItemWidth:r,tableList:[],isdisplay:!e.idEdit,inputType:"select",selectOptions:[],isrequired:!e.idEdit,...e.isform?{}:{customRender:({text:l,record:a})=>a.eigenValueName}},{title:"工况参数",dataIndex:"workConditionParams",columnWidth:120,formItemWidth:r,isrequired:!e.idEdit,isdisplay:!e.idEdit,afterContent:!0,headerOperations:{filters:[],filterDataIndex:["workConParameterName"]},inputType:"select",selectOptions:[{label:"功率(KW)",value:"功率"},{label:"转速(RPM)",value:"转速"},{label:"温度(°C)",value:"温度"}],...e.isform?{}:{customRender:({text:l,record:a})=>a.workConParameterName}},{title:"工况下限",dataIndex:"lowerLimitValue",columnWidth:80,formItemWidth:r,isdisplay:!e.idEdit,validateRules:q({title:"工况下限",type:"number",required:!e.idEdit})},{title:"工况上限",dataIndex:"upperLimitValue",columnWidth:80,formItemWidth:r,isdisplay:!e.idEdit,validateRules:q({title:"工况下限",type:"number",required:!e.idEdit})},{title:"正向注意",dataIndex:"warnValue",columnWidth:80,formItemWidth:r,validateRules:[{pattern:/^[0-9]+(\.[0-9]+)?$/,message:"请输入数字"},{validator:E,trigger:"change"}]},{title:"正向危险",dataIndex:"alarmValue",columnWidth:80,formItemWidth:r,validateRules:[{pattern:/^[0-9]+(\.[0-9]+)?$/,message:"请输入数字"},{validator:E,trigger:"change"}]},{title:"反向注意",dataIndex:"reverseWarnValue",columnWidth:80,formItemWidth:r,validateRules:[{pattern:/^[0-9]+(\.[0-9]+)?$/,message:"请输入数字"},{validator:F,trigger:"change"}]},{title:"反向危险",dataIndex:"reverseAlarmValue",columnWidth:80,formItemWidth:r,validateRules:[{pattern:/^[0-9]+(\.[0-9]+)?$/,message:"请输入数字"},{validator:F,trigger:"change"}]}]},W=p(!1),T=p(""),d=p(""),b=p(),L=p({}),f=p([]),x=p(!1),_=pe(),c=p(_.params.id),R=p(""),o=me({tableColumns:I(),tableData:[],batchApplyData:[],batchApplyKey:"",bathApplyResponse1:{}}),v=p({}),y=async e=>{W.value=!0,o.tableData=await t.fetchGetAlarmDefineList({turbineID:c.value}),o.tableColumns=I(),W.value=!1},M=async e=>{R.value&&await $.fetchDevTreedDevicelist({windParkID:R.value,useTobath:!0})},K=()=>{let e=h.findAncestorsWithNodes(c.value);e&&e.length&&e.length>1&&(R.value=e[e.length-2].id)};he(()=>_.params.id,async e=>{e&&(t.reset(),c.value=e,K(),await M(),y())},{immediate:!0});const j=fe(()=>d.value==="add"||d.value==="batchAdd"?"1200px":"600px"),P=()=>{x.value=!0},A=e=>{x.value=!1,f.value=[],L.value={},d.value="",T.value=""},z=e=>{const{tableKey:r,title:l,operateType:a}=e;d.value=a,T.value="批量添加报警定义",f.value=[...I({isform:!0})],J(),P()},U=async e=>{const{selectedkeys:r,record:l}=e;let a=[];if(!r||r.length===0)return;let s=t.alarmDefineList.filter(i=>r.includes(i.thresholdGroup));if(!s||s.length===0)return;for(let i=0;i<s.length;i++)a.push({windTurbineID:c.value,workConParameter:s[i].workConParameter,thresholdGroup:s[i].thresholdGroup,eigenValueID:s[i].eigenValueID,measLocationID:s[i].measLocationID});const n=await t.fetchBatchDeleteWarnRule({sourceData:a,targetTurbineIds:o.batchApplyData});n&&n.code===1?(y(),o.bathApplyResponse1=n.batchResults||{},m.success("删除成功")):m.error("删除失败:"+n.msg)},H=e=>{const{rowData:r,tableKey:l,title:a,operateType:s}=e;d.value=s,T.value="编辑报警定义",f.value=[...I({isform:!0,idEdit:!0})],L.value={...r},P()},S=async(e,r,l)=>{let a=f.value,s=e.value.value;if(e&&e.dataIndex&&e.dataIndex=="measLocationID"&&d.value=="batchAdd"){let n=await t.fetchGetEigenValueTypeList({turbineID:c.value,measLocId:s});if(d.value=="batchAdd"){if(e.index>=a[1].tableList.length)for(let i=a[1].tableList.length;i<=e.index;i++)a[1].tableList.push({});a[1].tableList[e.index].selectOptions=n,n&&n.length&&v.value.setTableFieldValue({formDataIndex:`eigenValueID[${e.index}]`,tableDataIndex:"eigenValueID",index:e.index,value:n&&n.length?n[0].value:""})}else a[1].selectOptions=n,b.value.setFieldValue("eigenValueID",n&&n.length?n[0].value:"")}},J=async e=>{let r=f.value,l=await t.fetchMeasLocations({turbineID:c.value});r[0].selectOptions=l;let a="";if(a=l&&l.length?l[0].value:"",l&&l.length){let s=await t.fetchGetEigenValueTypeList({turbineID:c.value,measLocId:a});r[1].selectOptions=s}},Q=async e=>{let r={...e,thresholdGroup:L.value.thresholdGroup,WindTurbineID:c.value,applyToAll:!1},l=await t.fetchEditWarnRule({sourceData:r,targetTurbineIds:o.batchApplyData});l&&l.code===1?(y(),o.bathApplyResponse1=l.batchResults||{},m.success("提交成功"),A()):m.error("提交失败:"+l.msg)},X=async e=>{let r={measLocationID:{option:["measLocType","measLocType"],value:"measLocationID"}},l=ve(e,{applyToAll:!1,windParkID:R.value,windTurbineID:c.value},r);if(l&&l.length){let a=await t.fetchWarnBatchAddWarnRulee({sourceData:l,targetTurbineIds:o.batchApplyData});a&&a.code===1?(y(),o.bathApplyResponse1=a.batchResults||{},m.success("提交成功"),A()):m.error("提交失败:"+a.msg)}},Y=async()=>{const e=await t.fetchInitTurbineWarn({turbineID:c.value});e&&e.code===1?(y(),m.success("初始化成功")):m.error("初始化失败:"+e.msg)},Z=async e=>{e.type&&e.type=="close"?(o.batchApplyData=[],o.batchApplyKey="",o[`bathApplyResponse${e.key}`]={}):(o.batchApplyData=e.turbines,o.batchApplyKey=e.key)};return O("deviceId",c),O("bathApplySubmit",Z),(e,r)=>{const l=ge,a=De,s=be;return V(),k(s,{spinning:W.value,size:"large"},{default:w(()=>[(V(),B("div",{key:c.value},[C(te,{ref:"table",size:"default","table-key":"1","table-title":"报警设置列表",borderLight:o.batchApplyKey=="1",bathApplyResponse:o.bathApplyResponse1,"table-columns":o.tableColumns,"table-operate":["delete","add","edit","batchDelete","batchAdd"],"record-key":"thresholdGroup","table-datas":o.tableData,onAddRow:z,onDeleteRow:U,onEditRow:H},{rightButtons:w(({selectedRowKeys:n})=>[C(l,{type:"primary",onClick:r[0]||(r[0]=i=>Y()),disabled:o.tableData.length},{default:w(()=>r[1]||(r[1]=[G(" 初始化报警定义 ",-1)])),_:1,__:[1]},8,["disabled"])]),default:w(()=>[r[2]||(r[2]=G(" > ",-1))]),_:1,__:[2]},8,["borderLight","bathApplyResponse","table-columns","table-datas"]),C(a,{maskClosable:!1,width:j.value,open:x.value,title:T.value,footer:"",onCancel:A},{default:w(()=>[d.value==="add"||d.value==="edit"?(V(),k(ae,{key:0,titleCol:f.value,ref_key:"formRef",ref:b,initFormData:L.value,onChange:S,onSubmit:Q},null,8,["titleCol","initFormData"])):d.value==="batchAdd"?(V(),k(le,{key:1,ref_key:"tableFormRef",ref:v,size:"default","table-key":"0","table-columns":f.value,"table-operate":["copyUp","delete"],"table-datas":[],"noCopyUp-keys":["measLocationID","eigenValueID"],onSubmit:X,onHangeTableFormChange:S,onCancel:A},null,8,["table-columns"])):(V(),B("div",Ve))]),_:1},8,["width","open","title"])]))]),_:1},8,["spinning"])}}};export{$e as default};
