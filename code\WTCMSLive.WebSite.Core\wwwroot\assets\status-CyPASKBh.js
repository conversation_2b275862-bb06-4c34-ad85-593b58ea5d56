import{r as d,w as k,f as C,d as L,u as N,o as e,i as l,b as g,c as r,s as o,g as s,t as u,F as p,e as c,q as w,x}from"./index-DLKlSkMo.js";import{u as D}from"./collectionUnitMonitor-CAMmfqoi.js";import{S as A,b as F}from"./tools-B0tc7tWY.js";import{_ as P}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{_ as V}from"./index-pJrpCCGr.js";import"./initDefaultProps-DBdyDbXv.js";const B={class:"border padding totalInfo"},U={class:"totalNumber"},E={class:"totalList"},z={class:"clearfix"},M={class:"parkNumber pullLeft dauNormal"},q={key:0,class:"parkNumber pullLeft dauAlarm"},G={key:1,class:"parkNumber pullLeft dauAlarm"},R={key:2,class:"parkNumber pullLeft dauAlarm"},T={key:3,class:"parkNumber pullLeft dauAlarm"},j={key:4,class:"parkNumber pullLeft dauUnknown"},H={key:0,class:"clearfix parkList"},J={class:"title"},K={key:0,class:"clearfix"},O={__name:"status",setup(Q){const S=D(),f=N(),t=d({overallSummary:{}}),m=d(!1),_=async()=>{m.value=!0;const v=await S.fetchGetDauStatusCount();v.overallSummary?t.value=v:t.value={overallSummary:{}},m.value=!1};return k(()=>f.params.id,()=>{_()},{immediate:!0}),d([{name:"1号厂站",number:160,faultList:[{part:"机舱",name:"传感器故障",value:160,level:6},{part:"机舱",name:"通讯异常",value:10,level:5}]},{name:"3号厂站",number:160,faultList:[{part:"机舱",name:"传感器故障",value:160,level:6},{part:"机舱",name:"通讯异常",value:10,level:5},{part:"机舱",name:"传感器故障2",value:160,level:6},{part:"机舱",name:"通讯异常3",value:10,level:5}]},{name:"3号厂站",number:160,faultList:[{part:"机舱",name:"传感器故障",value:160,level:6},{part:"机舱",name:"通讯异常",value:10,level:5}]}]),(v,a)=>{const y=V,b=A;return e(),C(b,{spinning:m.value,size:"large"},{default:L(()=>[l("div",null,[l("div",B,[l("p",U,[a[0]||(a[0]=s("采集单元总数 ",-1)),l("b",null,u(t.value.overallSummary.totalCount),1),a[1]||(a[1]=s("台",-1))]),l("div",E,[l("ul",z,[l("li",M,[a[2]||(a[2]=s(" 正常",-1)),l("b",null,u(t.value.overallSummary.normalCount),1)]),t.value.overallSummary.sensorFaultCount?(e(),r("li",q,[a[3]||(a[3]=s("传感器故障",-1)),l("b",null,u(t.value.overallSummary.sensorFaultCount),1)])):o("",!0),t.value.overallSummary.communicationErrorCount?(e(),r("li",G,[a[4]||(a[4]=s("通讯异常",-1)),l("b",null,u(t.value.overallSummary.communicationErrorCount),1)])):o("",!0),t.value.overallSummary.rotSpdFaultCount?(e(),r("li",R,[a[5]||(a[5]=s("转速异常",-1)),l("b",null,u(t.value.overallSummary.rotSpdFaultCount),1)])):o("",!0),t.value.overallSummary.noDataArriveCount?(e(),r("li",T,[a[6]||(a[6]=s("无数据到达",-1)),l("b",null,u(t.value.overallSummary.noDataArriveCount),1)])):o("",!0),t.value.overallSummary.unknownCount?(e(),r("li",j,[a[7]||(a[7]=s("未知",-1)),l("b",null,u(t.value.overallSummary.unknownCount),1)])):o("",!0)])])]),g(y),t.value.windParkStatusList&&t.value.windParkStatusList.length?(e(),r("ul",H,[(e(!0),r(p,null,c(t.value.windParkStatusList,n=>(e(),r("li",{class:"border padding parkLi",key:n.windParkID},[l("p",J,[l("b",null,u(n.windParkName),1),l("span",null,[a[8]||(a[8]=s("采集单元",-1)),l("b",null,u(n.statusSummary?n.statusSummary.totalCount:0),1),a[9]||(a[9]=s(" 台",-1))])]),n.nameStatistics&&n.nameStatistics.length?(e(),r("ul",K,[(e(!0),r(p,null,c(n.nameStatistics,i=>(e(),r("li",{key:i.dauName,class:w(["parkNumber","pullLeft",x(F)(i.statusValue).className])},[s(u(i.dauName)+": "+u(i.statusDescription),1),l("b",null,[s(u(i.count),1),a[10]||(a[10]=l("span",null,null,-1))])],2))),128))])):o("",!0)]))),128))])):o("",!0)])]),_:1},8,["spinning"])}}},I=P(O,[["__scopeId","data-v-0f6012e4"]]);export{I as default};
