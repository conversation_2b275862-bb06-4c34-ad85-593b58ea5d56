import{u as ve,c as ye,W as De}from"./table-C0LB8EJD.js";import{j as Y,y as be,r as l,u as we,U as he,h as ge,w as ke,f as y,d as i,A as Ie,z as S,o as m,i as M,b as x,c as Oe,F as _e,e as Te,g as _,t as xe,s as P,m as n}from"./index-DLKlSkMo.js";import{O as Pe}from"./index-ChYhAQZ_.js";import{W as Ce}from"./index-ZdxrKWAB.js";import{S as Le,d as C,t as We,a as Fe}from"./tools-B0tc7tWY.js";import{u as Ne,g as Re}from"./configRoot-CQn-dVve.js";import{u as Se}from"./model-CnJJsXga.js";import{u as Me}from"./devTree-BwbHtyaz.js";import{D as Ue,a as qe}from"./index-CMmDaUgW.js";import{B as Ae}from"./index-bX4hVoaz.js";import{M as Be}from"./index-BJVFa-Ub.js";import{_ as Ee}from"./index-Bh5jQV6z.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./ActionButton-DDquCBiw.js";import"./styleChecker-tOiS7TSB.js";import"./initDefaultProps-DBdyDbXv.js";import"./shallowequal-C1uyuyZ_.js";import"./index-W_N8ii3N.js";import"./index-DzeUeE5s.js";import"./index-C5qlGDhC.js";import"./index-pJrpCCGr.js";import"./index-Big_Av0Y.js";/* empty css                                                              */import"./useRefs-DyW9tToP.js";const Ve={class:"border"},$e={style:{float:"left"}},g=320,L="YYYY-MM-DD",vt={__name:"device",setup(je){const k=Se(),o=ve(),b=Ne(),s=Me(),W=e=>[{title:"设备编号",dataIndex:"windTurbineCode",columnWidth:"130",formItemWidth:g,hidden:e&&e.edit,isrequired:!0,headerOperations:{sorter:!0},columnOperate:{type:"number"}},{title:"设备名称",dataIndex:"windTurbineName",columnWidth:"130",formItemWidth:g,isrequired:!0,columnOperate:{type:"number"}},{title:"设备型号",dataIndex:"windTurbineModel",columnWidth:"140",formItemWidth:g,inputType:"select",isrequired:!0,selectOptions:[],disabled:e&&e.edit&&!e.canEditModel,headerOperations:{filters:[]}},{title:"部件",dataIndex:"componentIds",columnWidth:"200",formItemWidth:g,inputType:"select",mode:"tags",isrequired:!0,selectOptions:[],slotName:"part",hidden:e&&e.edit,...e&&e.isForm?{}:{customRender:({record:t})=>t.componentName?Ie("span",{style:{textAlign:"left",display:"block",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",maxWidth:"500px"},title:t.componentName.join(",")},t.componentName.join(",")):""}},{title:"投递日期",dataIndex:"operationalDate",columnWidth:"120",formItemWidth:g,inputType:"datepicker",timeFormat:L,headerOperations:{sorter:!0,date:!0}},{title:"主控IP",dataIndex:"mcsIP",columnWidth:"180",formItemWidth:g,hidden:e&&e.edit,columnOperate:{type:"ip"},validateRules:C({type:"ip",title:"主控IP",required:!0})},{title:"设备坐标",dataIndex:"location",formItemWidth:g,validateRules:[{pattern:/^(\-?\d+(\.\d+)?),\s*(\-?\d+(\.\d+)?)$/,message:"请输入两个数字并用英文逗号隔开!"}]}],H=Y(()=>c.noOperatesOfUser&&c.noOperatesOfUser.length&&c.noOperatesOfUser.includes("add")?["edit"]:["edit","delete","add","batchDelete","batchAdd"]),J={strokeColor:{"0%":"#108ee9","100%":"#87d068"},strokeWidth:3,format:e=>`${parseFloat(e.toFixed(2))}%`,class:"test"},B=window.localStorage.getItem("templateManagement")==="true",E=l(null),F=l([]),I=l(""),r=l(""),w=l({}),V=l([]),u=l([]),N=l(!1),$=we(),p=l($.params.id),K=l({}),T=l(!1),c=be({partValue:"",partList:[],noOperatesOfUser:he()});let U=[...Re({isEdit:!0})];const j=(e={})=>[{label:"名称",value:e.windParkName},{label:"编号",value:e.windParkCode},{label:"投运日期",value:e.operationalDate?S(e.operationalDate).format(L):""},{label:"联系人",value:e.contactMan},{label:"联系人电话",value:e.contactTel},{label:"区域",value:`${e.country} - ${e.area}`},{label:"地址",value:e.address},{label:"经纬度",value:e.location},{label:"邮编",value:e.postCode},{label:"厂站概况",value:e.description}],z=l(W()),Q=Y(()=>r.value==="batchAdd"?"1200px":"600px"),G=l([j({})]),O=async e=>{p.value&&(T.value=!0,V.value=await o.fetchDevTreedDevicelist({windParkID:p.value}),T.value=!1,z.value=W())},q=async()=>{if(p.value){const e=await o.fetchParkInfo({windParkID:p.value});K.value=e,G.value=j(e)}};ge(()=>{q(),O()}),ke(()=>$.params.id,e=>{o.reset(),p.value=e,q(),O()});const X=async()=>{r.value="editPark",I.value="编辑厂站信息";const e=o.parkInfo;let t=e.operationalDate?S(e.operationalDate,L):"";w.value={...e,operationalDate:t},b.groupCompanyList&&b.groupCompanyList.length>0?U[1].selectOptions=b.groupCompanyList:await Z(),u.value=[...U],R()},Z=async()=>{const e=await b.fetchGroupCompanyList();e&&e.length>0&&(U[1].selectOptions=e)},A=async()=>{k.modelOptions&&k.modelOptions.length>0||await k.fetchModellist()},ee=async()=>{(!o.comPonentList||o.comPonentList.length<1)&&await o.fetchGetAllComPonentList()},R=()=>{N.value=!0},h=e=>{N.value=!1,c.partList=[],c.partValue="",w.value={},u.value=[],I.value="",r.value=""},te=async e=>{const{operateType:t}=e;r.value=t,I.value="批量增加设备",await A(),await ee();let a=W({isForm:!0});a[2].selectOptions=k.modelOptions,a[3].selectOptions=o.comPonentList,u.value=[...a],R()},ae=async(e={})=>{const{selectedkeys:t}=e,a=await o.fetchDeletetDevice(t);a&&a.code===1?(O(),h(),n.success("提交成功"),s.getDevTreeDatas()):n.error("提交失败:"+a.msg)},oe=async e=>{const{rowData:t,operateType:a}=e;r.value=a;let f=t.operationalDate?S(t.operationalDate,L):"";w.value={...t,componentIds:t.componentIds&&t.componentIds.length?t.componentIds.split(","):[],operationalDate:f},I.value="编辑设备",await A();let d=W({edit:!0,isForm:!0,canEditModel:c.noOperatesOfUser&&c.noOperatesOfUser.length});d[2].selectOptions=k.modelOptions,d[3].inputType="checkboxGroup",u.value=[...d],R()},se=async()=>{r.value="copyDevice",I.value="复制设备",await s.getTemplateParkList(),await b.fetchParkList(),await A();let e=[],t=[];s.templateDeviceList&&s.templateDeviceList.length>0&&(e.push({label:"模版风场",value:s.templateDeviceList[0].id}),s.templateDevicoptions&&s.templateDevicoptions.length>0&&(t=s.templateDevicoptions)),e=[...e,...b.parkOptions];let a=ce({parklist:e,turbineList:t,modelList:k.modelOptions});u.value=a,R()},ie=async e=>(F.value=[e],!1),le=async e=>{if(!e.file||e.fileList.length<1){n.error("请选择文件");return}T.value=!0;const t=new FormData;t.append("file",e.file);let a=await o.fetchTemplateUpload(t);a.code===1?(F.value=[],n.success("上传成功")):n.error("上传失败！"+a.msg),T.value=!1},ne=()=>{console.log("导出"),o.fetchTemplateDownload({parkId:p.value})},re=async()=>{o.fetchExportDauConfig({parkId:p.value})},de=async(e,t,a)=>{if(e.dataIndex==="windParkID"){if(e.value==s.templateDeviceList[0].windParkID)u.value[0].rows[1].cols[0].selectOptions=s.templateDevicoptions||[];else{let f=await o.fetchDevTreedDevicelist({windParkID:e.value});if(f&&f.length>0){let d=Fe(f,{label:"windTurbineName",value:"windTurbineID"},{nother:!0});u.value[0].rows[1].cols[0].selectOptions=d}else u.value[0].rows[1].cols[0].selectOptions=[]}E.value.setFieldValue("_windTurbineIDOld",null)}},ue=async e=>{if(r.value=="editPark"){const t=await b.fetchEditWindparkInformation({...e,windParkID:p.value,windParkGroupName:e.windParkName});t&&t.code===1?(q(),n.success("提交成功"),e.windParkName!==o.parkInfo.windParkName&&s.getDevTreeDatas(),h()):n.error("提交失败:"+t.msg)}else if(r.value=="copyDevice"){const t=await o.fetchCopyTurbine({...e,_curParkId:p.value,_prefix:e._prefix||"",_suffix:e._suffix||"",startNum:e.startNum||""});t&&t.code===1?(n.success("复制成功"),O(),h(),s.getDevTreeDatas()):n.error("提交失败:"+t.msg)}else{let t=[{...e,componentIds:e.componentIds&&e.componentIds.length?e.componentIds.join(","):"",windTurbineID:w.value.windTurbineID,windParkID:w.value.windParkId}];const a=await o.fetchEditDevices(t);a&&a.code===1?(O(),n.success("提交成功"),e.windTurbineName!==w.value.windTurbineName&&s.getDevTreeDatas(),h()):n.error("提交失败:"+a.msg)}},pe=async e=>{if(r.value=="batchAdd"){let t={WindTurbineID:"",WindParkId:p.value},f=We(e).map(D=>({...D,...t,componentIds:D.componentIds&&D.componentIds.length?D.componentIds.join(","):"",operationalDate:D.operationalDate?S(D.operationalDate).format(L):""}));const d=await o.fetchAddDevice(f);d&&d.code===1?(O(),h(),n.success("提交成功"),s.getDevTreeDatas()):n.error("提交失败:"+d.msg)}},ce=(e={parklist:[],turbineList:[],modelList:[]})=>[{title:"设备模版",key:"设备模版",isrequired:!0,rows:[{cols:[{inputType:"select",selectOptions:e.parklist,formItemWidth:400,dataIndex:"windParkID",hasChangeEvent:!0,validateRules:C({title:"风场",required:!0})}]},{cols:[{dataIndex:"_windTurbineIDOld",inputType:"select",selectOptions:e.turbineList,formItemWidth:400,validateRules:C({title:"设备",required:!0})}]}]},{title:"复制数量",key:"复制数量",isrequired:!0,rows:[{cols:[{dataIndex:"_num",type:"input",formItemWidth:400,validateRules:C({type:"integer",title:"复制数量",required:!0})}]}]},{title:"复制后机组名称",key:"机组名称模板",rows:[{cols:[{title:"前缀",dataIndex:"_prefix",type:"input",formItemWidth:160},{title:"后缀",dataIndex:"_suffix",type:"input",formItemWidth:160}]},{cols:[{dataIndex:"startNum",title:"起始编号",type:"input",formItemWidth:140}]}]},{title:"机组型号",key:"机组型号",isrequired:!0,rows:[{cols:[{dataIndex:"turbineModel",inputType:"select",formItemWidth:400,selectOptions:e.modelList,validateRules:C({title:"机组型号",required:!0})}]}]}];return(e,t)=>{const a=Ae,f=qe,d=Ue,D=Ee,me=Be,fe=Le;return m(),y(fe,{spinning:T.value,size:"large"},{default:i(()=>[M("div",null,[x(ye,{tableTitle:"厂站信息",defaultCollapse:!0,batchApply:!1},{rightButtons:i(()=>[c.noOperatesOfUser.includes("editPark")?P("",!0):(m(),y(a,{key:0,type:"primary",onClick:t[0]||(t[0]=v=>X())},{default:i(()=>t[5]||(t[5]=[_(" 编辑 ",-1)])),_:1,__:[5]}))]),content:i(()=>[M("div",Ve,[x(d,{column:5,size:"small"},{default:i(()=>[(m(!0),Oe(_e,null,Te(G.value,v=>(m(),y(f,{label:v.label,key:v.label},{default:i(()=>[_(xe(v.value),1)]),_:2},1032,["label"]))),128))]),_:1})])]),_:1}),M("div",null,[x(De,{ref:"table",size:"default","table-key":"1","table-title":"设备列表","table-columns":z.value,"table-operate":H.value,"record-key":"windTurbineID","table-datas":V.value,onAddRow:te,onDeleteRow:ae,onEditRow:oe,noBatchApply:!0},{rightButtons:i(()=>[M("span",$e,[B?(m(),y(D,{key:0,"file-list":F.value,"onUpdate:fileList":t[1]||(t[1]=v=>F.value=v),name:"file",progress:J,onChange:le,"before-upload":ie,"show-upload-list":!1},{default:i(()=>[x(a,{type:"primary",style:{float:"none"}},{default:i(()=>t[6]||(t[6]=[_(" 导入 ",-1)])),_:1,__:[6]})]),_:1},8,["file-list"])):P("",!0)]),B?(m(),y(a,{key:0,type:"primary",onClick:t[2]||(t[2]=v=>ne())},{default:i(()=>t[7]||(t[7]=[_(" 导出 ",-1)])),_:1,__:[7]})):P("",!0),c.noOperatesOfUser.includes("copyDevice")?P("",!0):(m(),y(a,{key:1,type:"primary",onClick:t[3]||(t[3]=v=>se())},{default:i(()=>t[8]||(t[8]=[_(" 复制设备 ",-1)])),_:1,__:[8]})),c.noOperatesOfUser.includes("exportConfigers")?P("",!0):(m(),y(a,{key:2,type:"primary",onClick:t[4]||(t[4]=v=>re())},{default:i(()=>t[9]||(t[9]=[_(" 导出配置 ",-1)])),_:1,__:[9]}))]),_:1},8,["table-columns","table-operate","table-datas"])]),x(me,{maskcolsable:!1,width:Q.value,open:N.value,title:I.value,footer:"",onCancel:h},{default:i(()=>[r.value==="batchAdd"?(m(),y(Ce,{key:0,ref:"table",size:"default","table-key":"0","table-columns":u.value,"table-operate":["copyUp","delete"],"table-datas":[],onSubmit:pe,onCancel:h},{footer:i(()=>t[10]||(t[10]=[])),_:1},8,["table-columns"])):(m(),y(Pe,{key:N.value,titleCol:u.value,initFormData:w.value,ref_key:"operateFormRef",ref:E,onChange:de,onSubmit:ue,formlayout:r.value=="copyDevice"?"table":"horizontal"},null,8,["titleCol","initFormData","formlayout"]))]),_:1},8,["width","open","title"])])]),_:1},8,["spinning"])}}};export{vt as default};
