import{C as o,dy as s,dz as c,dA as l,dB as n,dC as a}from"./index-DLKlSkMo.js";import{a as i}from"./tools-B0tc7tWY.js";const d=o("role",{state:()=>({rolelist:[],roleOptions:[],modulelist:[]}),actions:{reset(){this.$reset()},async fetchGetrolelist(r){try{const e=await a(r);this.rolelist=e;let t=i(e,{label:"roleName",value:"roleID"});return this.roleOptions=t,e}catch(e){throw console.error("获取失败:",e),e}},async fetchGetmodulelist(r){try{let e=await n(r);return this.modulelist=e,e}catch(e){throw console.error("获取失败:",e),e}},async fetchAddrole(r){try{return await l(r)}catch(e){throw console.error("获取失败:",e),e}},async fetchEditrole(r){try{return await c(r)}catch(e){throw console.error(e),e}},async fetchDeleterole(r){try{return await s(r)}catch(e){throw console.error(e),e}}}});export{d as u};
