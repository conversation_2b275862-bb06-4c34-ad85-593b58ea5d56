# JWT续签功能测试说明

## 功能概述
已成功实现JWT Token续签功能，包括：

1. **JwtHelper类** - 封装JWT生成和验证逻辑
2. **RefreshToken接口** - 提供Token续签服务
3. **RefreshTokenResponse类** - 续签响应数据结构
4. **配置支持** - 支持续签宽限期配置

## API接口

### 续签接口
- **URL**: `POST /api/home/<USER>
- **认证**: 需要Bearer Token
- **请求头**: `Authorization: Bearer {current_token}`
- **响应**: 
```json
{
  "code": 1,
  "msg": "Token续签成功",
  "data": {
    "token": "new_jwt_token_here",
    "expiration": "2024-01-01T12:00:00Z",
    "userId": "user123",
    "role": "admin"
  }
}
```

## 测试步骤

### 1. 登录获取Token
```bash
curl -X POST "http://localhost:803/api/home/<USER>" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "account=your_username&password=your_password"
```

### 2. 使用Token续签
```bash
curl -X POST "http://localhost:803/api/home/<USER>" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 3. 验证新Token
使用返回的新Token调用其他需要认证的API接口。

## 配置说明

在 `appsettings.json` 中的JWT配置：
```json
{
  "JwtSettings": {
    "SecretKey": "your_secret_key",
    "Issuer": "WTCMSLive",
    "Audience": "WTCMSLiveUsers",
    "ExpirationInMinutes": 60,
    "RefreshGracePeriodInMinutes": 10
  }
}
```

- `ExpirationInMinutes`: Token有效期（分钟）
- `RefreshGracePeriodInMinutes`: 续签宽限期（分钟），在Token过期前多长时间允许续签

## 安全特性

1. **Token验证**: 验证Token的签名、发行者、受众等
2. **宽限期控制**: 只允许在过期前指定时间内续签
3. **用户信息保持**: 续签后保持原有的用户身份和角色信息
4. **错误处理**: 完善的错误处理和日志记录

## 前端集成建议

```javascript
// 检查Token是否即将过期并自动续签
async function checkAndRefreshToken() {
    const token = localStorage.getItem('token');
    if (!token) return false;
    
    // 解析Token获取过期时间
    const payload = JSON.parse(atob(token.split('.')[1]));
    const expTime = payload.exp * 1000;
    const now = Date.now();
    
    // 如果Token在10分钟内过期，进行续签
    if (expTime - now < 10 * 60 * 1000) {
        try {
            const response = await fetch('/api/home/<USER>', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            
            const result = await response.json();
            if (result.code === 1) {
                localStorage.setItem('token', result.data.token);
                return true;
            }
        } catch (error) {
            console.error('Token续签失败:', error);
        }
    }
    return false;
}
```

## 注意事项

1. 续签接口需要有效的JWT Token
2. Token必须在宽限期内才能续签
3. 续签后会生成全新的Token，旧Token立即失效
4. 建议在Token过期前主动续签，避免用户会话中断
