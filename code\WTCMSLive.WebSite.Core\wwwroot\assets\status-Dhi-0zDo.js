import{u as O,c as R,W}from"./table-C0LB8EJD.js";import{N as K}from"./noPermission-B0R9cMlR.js";import{r as p,u as j,y as P,w as G,f,x as y,d as o,z as $,o as r,i as a,b as x,c as h,F as T,e as E,g as u,t as c,s as H,A as J}from"./index-DLKlSkMo.js";import{S as q,g as l,c as Q}from"./tools-B0tc7tWY.js";import{u as X}from"./statusMonitor-8Q8D63vG.js";import{_ as Z}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{D as tt,a as et}from"./index-CMmDaUgW.js";import"./index-ChYhAQZ_.js";import"./index-pJrpCCGr.js";import"./index-Big_Av0Y.js";import"./styleChecker-tOiS7TSB.js";import"./index-bX4hVoaz.js";import"./initDefaultProps-DBdyDbXv.js";import"./index-W_N8ii3N.js";import"./shallowequal-C1uyuyZ_.js";import"./index-C5qlGDhC.js";import"./ActionButton-DDquCBiw.js";import"./index-DzeUeE5s.js";const at={class:"border"},st={class:"clearfix totalList"},lt={class:"totalNumber pullLeft"},nt={class:"pullLeft clearfix"},ot={class:"parkNumber pullLeft redBg"},rt={class:"parkNumber pullLeft yellowBg"},ut={class:"parkNumber pullLeft greenBg"},it=["src","title","alt"],I="YYYY-MM-DD",ct={__name:"status",setup(mt){const B=O(),U=X();let z=window.localStorage.getItem("role")?JSON.parse(window.localStorage.getItem("role")).moduleIds:[],w=window.localStorage.getItem("token")?z.includes("21"):!0;const N=j(),b=p(!1),D=(t={})=>[{label:"名称",value:t.windParkName},{label:"编号",value:t.windParkCode},{label:"投运日期",value:t.operationalDate?$(t.operationalDate).format(I):""},{label:"设备总数",value:t.windTurbineList?t.windTurbineList.length:0},{label:"联系人",value:t.contactMan},{label:"联系人电话",value:t.contactTel},{label:"区域",value:`${t.country} - ${t.area}`},{label:"地址",value:t.address},{label:"经纬度",value:t.location},{label:"邮编",value:t.postCode},{label:"厂站概况",value:t.description}],C=p([D({})]),s=p({}),m=p(N.params.id),g=P({tableData:[]}),v=P({}),M=async()=>{if(m.value){b.value=!0;const t=await B.fetchParkInfo({windParkID:m.value});C.value=D(t),b.value=!1}},V=t=>{const e=`${t.componentName}_${l(t.status).text}`;return v[e]?v[e]:(L(t),"/componentStatus/unknown.png")},Y=t=>{t.forEach(e=>{e.componentList&&e.componentList.forEach(n=>{L(n)})})},L=async t=>{const e=`${t.componentName}_${l(t.status).text}`;let n=`/componentStatus/${t.componentName}_${l(t.status).text}.png`;const S=await Q(n)?n:"/componentStatus/unknown.png";v[e]=S},A=[{title:"设备名称",dataIndex:"windTurbineName",canEdit:!0,align:"center"},{title:"设备状态",dataIndex:"turbineStatus",align:"center",headerOperations:{filters:[{text:"正常",value:3},{text:"注意",value:5},{text:"危险",value:6},{text:"未知",value:2}]},customRender:({record:t})=>J("span",{style:{color:l(t.turbineStatus).color}},l(t.turbineStatus).text)},{title:"部件状态",dataIndex:"partStatus",canEdit:!0,align:"center",otherColumn:!0},{title:"设备状态更新时间",dataIndex:"turbineStatusUpdateTime",canEdit:!0,align:"center",headerOperations:{sorter:!0,date:!0},customRender:({record:t})=>t.turbineStatusUpdateTime?$(t.turbineStatusUpdateTime).format(I):""}],F=async t=>{s.value=await U.fetchGetDevStatusCount({WindParkID:m.value}),g.tableData=s.value.turbineDetailList||[],Y(g.tableData)};return G(()=>N.params.id,t=>{t&&w&&(m.value=t,F(),M())},{immediate:!0}),(t,e)=>{const n=et,k=tt,S=q;return y(w)?(r(),f(S,{key:0,spinning:b.value,size:"large"},{default:o(()=>[a("div",null,[x(R,{tableTitle:"厂站信息",defaultCollapse:!0,batchApply:!1},{content:o(()=>[a("div",at,[x(k,{column:5,size:"small"},{default:o(()=>[(r(!0),h(T,null,E(C.value,d=>(r(),f(n,{label:d.label,key:d.label},{default:o(()=>[u(c(d.value),1)]),_:2},1032,["label"]))),128))]),_:1})])]),_:1}),a("div",st,[a("p",lt,[e[0]||(e[0]=u(" 设备总数(台) ",-1)),a("span",null,c(s.value.turbineStatusSummary?s.value.turbineStatusSummary.totalCount:0),1)]),a("ul",nt,[a("li",ot,[e[1]||(e[1]=u("危险",-1)),a("span",null,c(s.value.turbineStatusSummary?s.value.turbineStatusSummary.dangerCount:0),1)]),a("li",rt,[e[2]||(e[2]=u("注意",-1)),a("span",null,c(s.value.turbineStatusSummary?s.value.turbineStatusSummary.warningCount:0),1)]),a("li",ut,[e[3]||(e[3]=u("正常",-1)),a("span",null,c(s.value.turbineStatusSummary?s.value.turbineStatusSummary.normalCount:0),1)])])]),a("div",null,[x(W,{ref:"table",size:"default","table-key":"0","table-title":"设备状态监测",noheader:!0,"table-columns":A,"table-operate":[],"record-key":"name","table-datas":g.tableData,noBatchApply:!0,defaultPageSize:50},{otherColumn:o(({column:d,record:_,text:dt})=>[_.componentList&&_.componentList.length?(r(!0),h(T,{key:0},E(_.componentList,i=>(r(),h("img",{class:"componentImg",src:V(i),title:`${i.componentName}${y(l)(i.status).text}`,alt:`${i.componentName}${y(l)(i.status).text}`},null,8,it))),256)):H("",!0)]),_:1},8,["table-datas"])])])]),_:1},8,["spinning"])):(r(),f(K,{key:1},{default:o(()=>e[4]||(e[4]=[u(" 无权限 ",-1)])),_:1,__:[4]}))}}},Tt=Z(ct,[["__scopeId","data-v-9a8c73c1"]]);export{Tt as default};
