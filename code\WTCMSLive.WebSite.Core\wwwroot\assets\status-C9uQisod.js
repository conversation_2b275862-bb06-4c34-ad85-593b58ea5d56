import{c as Q,W as M}from"./table-C0LB8EJD.js";import{N as X}from"./noPermission-B0R9cMlR.js";import{r as g,u as Z,z as p,y as B,w as ee,B as te,A as f,f as x,x as c,d,o as i,i as D,b,c as h,F as H,e as ae,g as R,t as ne,s as A}from"./index-DLKlSkMo.js";import{u as le}from"./configDevice-Bk4BneTD.js";import{u as re}from"./statusMonitor-8Q8D63vG.js";import{u as se}from"./devTree-BwbHtyaz.js";import{S as oe,g as o,c as E}from"./tools-B0tc7tWY.js";import{_ as me}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{D as ie,a as ue}from"./index-CMmDaUgW.js";import"./index-ChYhAQZ_.js";import"./index-pJrpCCGr.js";import"./index-Big_Av0Y.js";import"./styleChecker-tOiS7TSB.js";import"./index-bX4hVoaz.js";import"./initDefaultProps-DBdyDbXv.js";import"./index-W_N8ii3N.js";import"./shallowequal-C1uyuyZ_.js";import"./index-C5qlGDhC.js";import"./ActionButton-DDquCBiw.js";import"./index-DzeUeE5s.js";const ce={class:"border"},de={class:"componentStatusBox"},ge=["src","title","alt"],pe=["src","title","alt"],fe={__name:"status",setup(ve){const P=le(),S=re(),z=se();let L=window.localStorage.getItem("role")?JSON.parse(window.localStorage.getItem("role")).moduleIds:[],N=window.localStorage.getItem("token")?L.includes("21"):!0;const T=(e={})=>[{label:"设备名称",value:e.windTurbineName},{label:"设备编号",value:e.windTurbineCode},{label:"设备型号",value:e.windTurbineModel},{label:"额定功率(KW)",value:e.ratedPower},{label:"设备厂商",value:e.manufacturer}],I=Z(),w=g(""),u=g(I.params.id),_=g(!1),y=g([p().subtract(3,"months"),p()]),m=B({filterData:[],tableData:[],tableData1:[],tableData2:[]}),k=g([T({})]),v=B({}),O=()=>{let e=z.findAncestorsWithNodes(u.value);e&&e.length&&e.length>1&&(w.value=e[e.length-2].id)},V=async e=>{if(u.value){const t=await P.fetchDeviceInfo({turbineID:u.value});k.value=T(t)}},W=async()=>{const e=await S.fetchGetTurbineStatusCount({TurbineID:u.value,WindParkID:w.value});if(!e||!e.length){m.filterData=[],m.tableData=[];return}let t=[],r=[],n="devSegmentName";e.forEach(l=>{r.indexOf(l[n])===-1&&l[n]&&l[n]!==""&&(r.push(l[n]),t.push({text:l[n],value:l[n]}))}),m.filterData=t,K(m.tableData),U(e)},Y=(e,t)=>{let r=`${e.devSegmentName}_${o(e.alarmDegree).text}`;return t&&(r=`${e.devSegmentName1}_${o(e.alarmDegree1).text}`),v[r]?v[r]:($(e),"/componentStatus/unknown.png")},K=e=>{!e||!e.length||e.forEach(t=>{$(t)})},$=async e=>{let t=`/componentStatus/${e.devSegmentName}_${o(e.alarmDegree).text}.png`,r=`/componentStatus/${e.devSegmentName1}_${o(e.alarmDegree1).text}.png`;const n=`${e.devSegmentName}_${o(e.alarmDegree).text}`,l=`${e.devSegmentName1}_${o(e.alarmDegree1).text}`,a=await E(t),s=await E(r),C=a?t:"/componentStatus/unknown.png",q=s?r:"/componentStatus/unknown.png";v[n]=C,v[l]=q},U=e=>{var r,n,l;if(!e||!e.length)return;const t=[];for(let a=0;a<e.length;a+=2)t.push({...e[a],devSegmentName1:(r=e[a+1])==null?void 0:r.devSegmentName,alarmUpdateTime1:(n=e[a+1])==null?void 0:n.alarmUpdateTime,alarmDegree1:(l=e[a+1])==null?void 0:l.alarmDegree});m.tableData=t},F=async()=>{_.value=!0,m.tableData2=await S.fetchGetSearchMonitorLog({turbineID:u.value,beginTime:y.value[0].format("YYYY-MM-DD"),endTime:y.value[1].format("YYYY-MM-DD")}),_.value=!1};ee(()=>I.params.id,e=>{e&&N&&(u.value=e,O(),F(),V(),W())},{immediate:!0});const G=te(()=>[{title:"部件状态",dataIndex:"devSegmentName",filters:m.filterData,otherColumn:!0},{title:"状态更新时间",dataIndex:"alarmUpdateTime",headerOperations:{sorter:!0,date:!0},customRender:({text:e,record:t})=>t.alarmUpdateTime?f("span",{},p(e).format("YYYY-MM-DD HH:mm:ss")):""},{title:"部件状态",dataIndex:"devSegmentName1",otherColumn:!0},{title:"状态更新时间",dataIndex:"alarmUpdateTime1",customRender:({text:e,record:t})=>t.alarmUpdateTime1?f("span",{},p(e).format("YYYY-MM-DD HH:mm:ss")):""}]),j=e=>{const{data:t}=e,{filters:r,sorter:n}=t;let l=S.deviceStatusList;r&&r.devSegmentName&&(l=l.filter(a=>r.devSegmentName.includes(a.devSegmentName))),n&&n.field&&n.order&&(l=l.sort((a,s)=>{if(n.order==="ascend")return new Date(a.alarmUpdateTime).getTime()-new Date(s.alarmUpdateTime).getTime();if(n.order==="descend")return new Date(s.alarmUpdateTime).getTime()-new Date(a.alarmUpdateTime).getTime()})),U(l)},J=[{title:"状态",dataIndex:"alarmDegree",width:80,headerOperations:{filters:[{text:"正常",value:3},{text:"注意",value:5},{text:"危险",value:6},{text:"未知",value:2}]},customRender:({record:e})=>e.alarmDegree?f("span",{style:{color:o(e.alarmDegree).color}},o(e.alarmDegree).text):""},{title:"详情",dataIndex:"logTitle",customRender:({text:e,record:t})=>t.logTitle?f("p",{style:{textAlign:"left"}},e):""},{title:"状态更新时间",dataIndex:"eventTime",width:160,headerOperations:{sorter:!0},customRender:({text:e,record:t})=>t.eventTime?f("span",{},p(e).format("YYYY-MM-DD HH:mm:ss")):""}];return(e,t)=>{const r=ue,n=ie,l=oe;return c(N)?(i(),x(l,{key:0,spinning:_.value,size:"large"},{default:d(()=>[D("div",null,[b(Q,{tableTitle:"设备信息",defaultCollapse:!0,batchApply:!1},{content:d(()=>[D("div",ce,[b(n,{column:5,size:"small"},{default:d(()=>[(i(!0),h(H,null,ae(k.value,a=>(i(),x(r,{label:a.label,key:a.label},{default:d(()=>[R(ne(a.value),1)]),_:2},1032,["label"]))),128))]),_:1})])]),_:1}),D("div",null,[D("div",de,[b(M,{ref:"table",size:"default","table-key":"0","table-title":"设备部件状态","table-columns":c(G),"table-operate":[],noBatchApply:!0,"record-key":"name","table-datas":m.tableData,onHandleTableChange:j},{otherColumn:d(({column:a,record:s,text:C})=>[s.devSegmentName?(i(),h(H,{key:0},[a.dataIndex==="devSegmentName"&&s.devSegmentName?(i(),h("img",{key:0,class:"componentImg",src:Y(s),title:`${s.devSegmentName}${c(o)(s.alarmDegree).text}`,alt:`${s.devSegmentName}${c(o)(s.alarmDegree).text}`},null,8,ge)):a.dataIndex==="devSegmentName1"&&s.devSegmentName1?(i(),h("img",{key:1,class:"componentImg",src:Y(s,"2"),title:`${s.devSegmentName1}${c(o)(s.alarmDegree1).text}`,alt:`${s.devSegmentName1}${c(o)(s.alarmDegree1).text}`},null,8,pe)):A("",!0)],64)):A("",!0)]),_:1},8,["table-columns","table-datas"])]),b(M,{ref:"table",size:"default","table-key":"0","table-title":"部件报警日志","table-columns":J,"table-operate":[],noBatchApply:!0,"record-key":"name","table-datas":m.tableData2},null,8,["table-datas"])])])]),_:1},8,["spinning"])):(i(),x(X,{key:1},{default:d(()=>t[0]||(t[0]=[R(" 无权限 ",-1)])),_:1,__:[0]}))}}},Ae=me(fe,[["__scopeId","data-v-c4d6ed66"]]);export{Ae as default};
