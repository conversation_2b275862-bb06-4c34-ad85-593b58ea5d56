import{C as c,a_ as n,a$ as a,b0 as i,b1 as h,b2 as l,b3 as p,b4 as d,b5 as u,b6 as L,b7 as y,b8 as w,b9 as M,ba as b,bb as f,bc as m,bd as D,be as g,bf as k,bg as C,bh as S,bi as v,bj as G,bk as W,bl as V,bm as P,bn as O,bo as A}from"./index-DLKlSkMo.js";import{a as t,e as s}from"./tools-B0tc7tWY.js";const I=c("configDevice",{state:()=>({deviceInfo:{},vibMeaslocationList:[],processMeaslocationList:[],componentList:[],sectionList:[],orientatioList:[],workCondMeasLocDicOptions:[],workCondMeasLocsList:[],enumWorkConDataSourceOptions:[],rotSpdMeasLocList:[],modbusMeasLocList:[],sVMParamTypeList:[],modbusMeasLocoptions:[]}),actions:{reset(){this.$reset()},async fetchDeviceInfo(o={}){try{const e=await A(o);return this.deviceInfo=e,e}catch(e){throw console.error("获取设备失败:",e),e}},async fetcheditOneDevice(o={}){try{return await O(o)}catch(e){throw console.error("编辑失败:",e),e}},async fetchGetVibMeaslocation(o={}){try{const e=await P(o);return e&&e.length>0?(this.getVibMeaslocation=e,e):[]}catch(e){throw console.error("获取失败:",e),e}},async fetchGetProcessMeaslocation(o={}){try{const e=await V(o);return e&&e.length>0?(this.processMeaslocationList=e,e):[]}catch(e){throw console.error("获取失败:",e),e}},async fetchGetOrientationList(o={}){try{const e=await W(o);let r=s(e,"key");return this.orientatioList=r,r}catch(e){throw console.error("获取失败:",e),e}},async fetchGetComponentList(o={}){try{const e=await G(o);if(e&&e.length>0){let r=t(e,{label:"componentName",value:"componentID"},{nother:!0});return this.componentList=r,r}return[]}catch(e){throw console.error("获取失败:",e),e}},async fetchGetSectionList(o={}){try{const e=await v(o);let r=s(e,"key");return this.sectionList=r,r}catch(e){throw console.error("获取失败:",e),e}},async fetchGetWorkCondMeasLocDic(o={}){try{const e=await S(o);let r=t(e);return this.workCondMeasLocDicOptions=r,r}catch(e){throw console.error("获取失败:",e),e}},async fetchGetEnumWorkConDataSource(o={}){try{const e=await C(o);let r=t(e,{label:"value",value:"key",text:"value"},{nother:!0});return this.enumWorkConDataSourceOptions=r,r}catch(e){throw console.error("获取失败:",e),e}},async fetchAddVibMeasLocs(o={}){try{return await k(o)}catch(e){throw console.error("批量添加失败:",e),e}},async fetchEditVibMeasLoc(o={}){try{return await g(o)}catch(e){throw console.error("编辑失败:",e),e}},async fetchDeleteVibMeasLocs(o={}){try{return await D(o)}catch(e){throw console.error("删除失败:",e),e}},async fetchAddProcessMeasLocs(o={}){try{return await m(o)}catch(e){throw console.error("批量添加失败:",e),e}},async fetchEditProcessMeasLoc(o={}){try{return await f(o)}catch(e){throw console.error("编辑失败:",e),e}},async fetchDeleteProcessMeasLocs(o={}){try{return await b(o)}catch(e){throw console.error("删除失败:",e),e}},async fetchGetWorkCondMeasLocs(o={}){try{const e=await M(o);return e&&e.length>0?(this.workCondMeasLocsList=e,e):[]}catch(e){throw console.error("获取失败:",e),e}},async fetchAddWorkingConditionMeaslocs(o={}){try{return await w(o)}catch(e){throw console.error("批量添加失败:",e),e}},async fetchEditWorkingConditionMeas(o={}){try{return await y(o)}catch(e){throw console.error("编辑失败:",e),e}},async fetchDeleteWorkingConditionMeasBatch(o={}){try{return await L(o)}catch(e){throw console.error("删除失败:",e),e}},async fetchGetRotSpdMeasLocList(o={}){try{const e=await u(o);return this.rotSpdMeasLocList=e,e}catch(e){throw console.error("删除失败:",e),e}},async fetchAddRotSpdMeaslocs(o={}){try{return await d(o)}catch(e){throw console.error("批量添加失败:",e),e}},async fetchEditRotSpdLoc(o={}){try{return await p(o)}catch(e){throw console.error("编辑失败:",e),e}},async fetchDeleteRotSpdLoc(o={}){try{return await l(o)}catch(e){throw console.error("删除失败:",e),e}},async fetchGetModbusMeasLocList(o){try{const e=await h(o);this.modbusMeasLocList=e;let r=t(e,{label:"measLocName",value:"measLocationID"},{nother:!0});return this.modbusMeasLocoptions=r,e}catch(e){throw console.error("获取失败:",e),e}},async fetchAddModbusMeasloc(o){try{return await i(o)}catch(e){throw console.error("操作失败:",e),e}},async fetchBatchDeleteMeasLoc(o){try{return await a(o)}catch(e){throw console.error("操作失败:",e),e}},async fetchGetSVMParamType(o){try{const e=await n(o);let r=t(e,{label:"value",value:"value"},{nother:!0});return this.sVMParamType=r,r}catch(e){throw console.error("操作失败:",e),e}}}});export{I as u};
