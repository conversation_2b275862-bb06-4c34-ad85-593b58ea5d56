import{c as Q,W as X}from"./table-C0LB8EJD.js";import{O as Z}from"./index-ChYhAQZ_.js";import{z as m,r as l,u as ee,y as ae,w as te,f as g,d as i,o as s,i as u,b as f,c as k,F as T,e as V,g as ne,t as Y,s as oe,q as le,A as S}from"./index-DLKlSkMo.js";import{S as re,g as P}from"./tools-B0tc7tWY.js";import{L as ie}from"./index-qC0ZfWaz.js";import{u as se}from"./statusMonitor-8Q8D63vG.js";import{u as me}from"./devTree-BwbHtyaz.js";import{_ as ue}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{D as ce,a as de}from"./index-CMmDaUgW.js";import{M as pe}from"./index-BJVFa-Ub.js";import{a as ve}from"./ActionButton-DDquCBiw.js";import"./styleChecker-tOiS7TSB.js";import"./index-bX4hVoaz.js";import"./initDefaultProps-DBdyDbXv.js";import"./index-W_N8ii3N.js";import"./shallowequal-C1uyuyZ_.js";import"./index-DzeUeE5s.js";import"./index-C5qlGDhC.js";import"./index-pJrpCCGr.js";import"./index-Big_Av0Y.js";const fe={class:"border"},he=["onClick"],_e={class:"modalMarkLines"},De=["onClick"],ge={class:"chartBox"},ke={key:1,class:"nodata"},be={__name:"eigenvalue",setup(Me){const b=se(),A=me(),B=(e={})=>[{label:"发电机转速(CMS)",value:e.windParkName},{label:"风速",value:e.windParkName},{label:"输出功率(KW)",value:e.windParkName},{label:"发电机转速(CMS)",value:e.windParkName},{label:"桨距角",value:e.windParkName},{label:"偏航角度",value:e.windParkName}];let h=[m().subtract(3,"month"),m()];const M=l(!1),y=ee(),L=l(""),N=l(""),c=l(y.params.id),_=l(h),C=l(!1),x=l(B({})),d=l({time:h}),p=l([]),n=l({}),a=ae({tableData:[],chartInformation:{title:"",legendArr:[]},currentMarkLine:{}}),O=()=>{let e=A.findAncestorsWithNodes(c.value);e&&e.length&&e.length>1&&(L.value=e[e.length-2].id)},F=async()=>{if(c.value){const e=await b.fetchGetWorkCondEVRT({windParkID:L.value,turbineID:c.value});e&&e.length&&(x.value=e)}},R=async()=>{M.value=!0,a.tableData=await b.fetchGetEigenValueRT({windParkID:L.value,turbineID:c.value}),M.value=!1};te(()=>y.params.id,e=>{e&&(c.value=e,O(),R(),F())},{immediate:!0});const W=async e=>{N.value=`${e.measLocationName}_${e.eigenValueName}`,d.value={...d.value,...e},await w(),E()},w=async()=>{const e=d.value;let o=await b.fetchGetTrendAnalyseByTime({turbineID:c.value,timeBegin:m(_.value[0]).format("YYYY-MM-DD"),timeEnd:m(_.value[1]).format("YYYY-MM-DD"),workConValue:"",workConParame:"",measType:e.dataType,EigenCode:e.eigenValueCode,measLoc:e.measLocationID,UseAlarmDef:!1});if(o&&o.length){const D={time:o[0].timeValueData,lineData:[{line:o[0].eigenValueData}],markLine:[]};let r=[];o[0].thresholdLineDatas&&o[0].thresholdLineDatas.length&&(r=[...r,...o[0].thresholdLineDatas.map(v=>({lineName:v.lineName,markLine:[v.warningLine,v.alarmLine]}))]),r&&r.length&&(a.currentMarkLine=r[0],p.value=r,D.markLine=r[0].markLine),n.value=D,a.chartInformation={title:`${e.measLocationName}`,legendArr:[e.eigenValueName]}}else n.value={},a.currentMarkLine={},p.value=[]},$=e=>{if(a.currentMarkLine&&a.currentMarkLine.lineName==e.lineName){a.currentMarkLine={},n.value={...n.value,markLine:[]};return}a.currentMarkLine=e,n.value={...n.value,markLine:e.markLine}},z=e=>{_.value=[m(e.time[0]).format("YYYY-MM-DD"),m(e.time[1]).format("YYYY-MM-DD")],w()},E=()=>{C.value=!0},q=e=>{C.value=!1,d.value={time:h},_.value=h},G=[{title:"部件",dataIndex:"compName",headerOperations:{filters:[]}},{title:"测量位置",dataIndex:"measLocationName",headerOperations:{filters:[]}},{title:"特征值",dataIndex:"eigenValueName",otherColumn:!0},{title:"数值",dataIndex:"eigenValue"},{title:"状态",dataIndex:"alarmDegree",headerOperations:{filters:[{text:"正常",value:3},{text:"注意",value:5},{text:"危险",value:6},{text:"未知",value:2}]},customRender:({record:e})=>e.alarmDegree?S("span",{style:{color:P(e.alarmDegree).color}},P(e.alarmDegree).text):""},{title:"更新时间",dataIndex:"acquisitionTime",headerOperations:{sorter:!0,date:!0},customRender:({record:e})=>e.acquisitionTime?S("span",{},m(e.acquisitionTime).format("YYYY-MM-DD HH:mm:ss")):""}],H=[{title:"选择时间",dataIndex:"time",inputType:"rangePicker",timeFormat:["YYYY-MM-DD","YYYY-MM-DD"],noMinLabelWidth:!0}],K={showToolbox:!0};return(e,o)=>{const D=de,r=ce,v=ve,j=pe,U=re;return s(),g(U,{spinning:M.value,size:"large"},{default:i(()=>[u("div",null,[f(Q,{tableTitle:"工况特征值详情",defaultCollapse:!0,batchApply:!1},{content:i(()=>[u("div",fe,[f(r,{column:4,size:"small"},{default:i(()=>[(s(!0),k(T,null,V(x.value,t=>(s(),g(D,{label:t.label,key:t.label},{default:i(()=>[ne(Y(t.value),1)]),_:2},1032,["label"]))),128))]),_:1})])]),_:1}),u("div",null,[f(X,{ref:"table",size:"default","table-key":"0","table-title":"特征值列表","table-columns":G,"table-operate":[],noBatchApply:!0,defaultPageSize:50,recordKey:t=>`${t.measLocationID}&&${t.eigenValueCode}`,"table-datas":a.tableData},{otherColumn:i(({column:t,record:I,text:J})=>[u("a",{onClick:Le=>W(I)},Y(J),9,he)]),_:1},8,["recordKey","table-datas"])]),f(j,{maskClosable:!1,destroyOnClose:!0,width:"800px",open:C.value,title:N.value,footer:"",onCancel:q},{default:i(()=>[f(Z,{titleCol:H,ref:"formRef",initFormData:d.value,formlayout:"inline",onSubmit:z},null,8,["initFormData"]),p.value&&p.value.length?(s(),g(v,{key:0,placement:"bottom",overlayClassName:"myPopover"},{content:i(()=>[u("ul",_e,[(s(!0),k(T,null,V(p.value,t=>(s(),k("li",{onClick:I=>$(t),class:le({active:t.lineName==a.currentMarkLine.lineName})},Y(t.lineName),11,De))),256))])]),default:i(()=>[o[0]||(o[0]=u("span",{class:"modalMarkLineBtn"},"报警规则",-1))]),_:1,__:[0]})):oe("",!0),u("div",ge,[n.value&&n.value.time&&n.value.time.length?(s(),g(ie,{key:0,boxId:"chart1",chartOptions:K,informations:a.chartInformation,chartData:n.value},null,8,["informations","chartData"])):(s(),k("div",ke,"暂无数据"))])]),_:1},8,["open","title"])])]),_:1},8,["spinning"])}}},qe=ue(be,[["__scopeId","data-v-d8720122"]]);export{qe as default};
