import{O as De,F as Ce}from"./index-ChYhAQZ_.js";import{r as T,y as _e,w as Me,j as Pe,f as le,d as M,u as Ne,o as h,i as x,b as C,c as I,F as V,e as U,t as L,s as G,g as H,q as ie,p as Oe,v as Fe,m as P}from"./index-DLKlSkMo.js";import{W as Le,_ as Be}from"./table-C0LB8EJD.js";import{W as Se}from"./index-ZdxrKWAB.js";import{d as l,S as Ae,f as Ve}from"./tools-B0tc7tWY.js";import{u as Ge}from"./devTree-BwbHtyaz.js";import{u as qe}from"./model-CnJJsXga.js";import{_ as We}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{M as $e}from"./index-BJVFa-Ub.js";import{F as Ee}from"./index-Big_Av0Y.js";import{B as ze}from"./index-bX4hVoaz.js";import"./index-pJrpCCGr.js";import"./index-W_N8ii3N.js";import"./styleChecker-tOiS7TSB.js";import"./initDefaultProps-DBdyDbXv.js";import"./shallowequal-C1uyuyZ_.js";import"./index-C5qlGDhC.js";import"./ActionButton-DDquCBiw.js";import"./index-DzeUeE5s.js";/* empty css                                                              */const Ue="/delete.png",ce="/addTrigger.png",N=320,He=[{label:"双馈式",value:1,text:"双馈式"},{label:"直驱式",value:0,text:"直驱式"},{label:"半直驱式",value:2,text:"半直驱式"}],ne=(v={isEdit:!1,structureTypes:He},m)=>[{title:"机组型号",dataIndex:"turbineModel",columnWidth:120,formItemWidth:N,disabled:v.isEdit,headerOperations:{sorter:!0}},{title:"结构类型",dataIndex:"structureType",columnWidth:120,formItemWidth:N,headerOperations:{filterOptions:v.structureTypes,filters:[]},inputType:"select",selectOptions:v.structureTypes||[],customRender:({text:D,record:O})=>{const F=v.structureTypes&&v.structureTypes.length?v.structureTypes.find(R=>R.value==O.structureType):{};return F?F.label:D}},{title:"额定功率(KW)",dataIndex:"ratedPower",columnWidth:100,formItemWidth:N,validateRules:l({type:"number",title:"额定功率",required:!0})},{title:"发电机额定转速(RPM)",dataIndex:"ratedGeneratorSpeed",columnWidth:80,formItemWidth:N,validateRules:[...l({type:"number",title:"发电机额定转速",required:!0}),{validator:m,trigger:"change"}]},{title:"发电机并网转速(RPM)",dataIndex:"gridConnectedGeneratorSpeed",columnWidth:100,formItemWidth:N,validateRules:[...l({type:"number",title:"发电机额定转速",required:!0}),{validator:m,trigger:"change"}]},{title:"叶片个数",dataIndex:"bladeNum",columnWidth:100,formItemWidth:N,validateRules:l({type:"number",title:"叶片个数",required:!0})},{title:"塔筒高度(m)",dataIndex:"towerHeight",columnWidth:100,formItemWidth:N,validateRules:l({type:"number",title:"塔筒高度",required:!0})},{title:"机组供应商",dataIndex:"manufactory",columnWidth:150,formItemWidth:N,headerOperations:{filters:[]}},{title:"部件参数配置",dataIndex:"otherColumn",columnHidden:!0,isdisplay:!1,width:135}],je=()=>({flangeLevel:[{label:"一层",value:1},{label:"二层",value:2},{label:"三层",value:3},{label:"四层",value:4},{label:"五层",value:5},{label:"六层",value:6}],zhuzhouType:[{label:"滚动",value:"滚动"},{label:"滑动",value:"滑动"}],pitchMethod:[{label:"液压变桨系统",value:"液压变桨系统"},{label:"电动变桨系统",value:"电动变桨系统"}],gearboxManufacturerOptions:[{label:"厂家一",value:1}],gearboxModelOptions:[],gearboxGearRatioOptions:[],generatorModel:[],generatorManufacturer:[{label:"厂家一",value:1}],manufacturer:[{label:"厂家一",value:1}],model:[],bearingModel:[],towerType:[{label:"钢塔",value:"钢塔"},{label:"柔塔",value:"柔塔"},{label:"混塔",value:"混塔"}],isCommutatorOrFrictionPlate:[{label:"是",value:"true"},{label:"否",value:"false"}]}),Ke=v=>{switch(v){case"机舱":return[{title:"主轴",key:"1",dataIndex:"mb",content:[{tableList:[{title:"",key:"2-1",dataIndex:"table3",addRowParentDataIndex:"mainBearingList",columns:[{title:"名称",dataIndex:"compType",width:75,rowSpan:[0,2],showText:!0},{title:"位置",width:75,dataIndex:"bearingName",showText:!0},{title:"厂家",dataIndex:"bearingManufacturer",width:160},{title:"型号",dataIndex:"bearingModel",width:160},{title:"类型",dataIndex:"bearingType",inputType:"select",optionname:"zhuzhouType",width:110},{title:"节圈直径",subTitle:"两个滚子到中心的距离",dataIndex:"bearingPitchDiameter",width:180,validateRules:l({type:"number",title:"两个滚子到中心的距离"})},{title:"滚动体直径",dataIndex:"bearingRollDiameter",validateRules:l({type:"number",title:"滚动体直径"})},{title:"滚动体个数",dataIndex:"bearingRollCount",validateRules:l({type:"integer",title:"滚动体个数"})},{title:"接触角",dataIndex:"bearingContactAngle",validateRules:l({type:"number",title:"接触角"})}],tableData:[{key:"1",compType:"主轴承",bearingName:"前轴承"},{key:"2",compType:"主轴承",bearingName:"后轴承"}]}]}]},{title:"发电机",dataIndex:"gen",key:"2",content:[{tableList:[{title:"",key:"3-0",dataIndex:"table3-0",columns:[{title:"厂家",dataIndex:"generatorManufacturer"},{title:"型号",dataIndex:"generatorModel"},{title:"磁极对数",dataIndex:"generatorPolePairs",validateRules:l({type:"integer",title:"磁极对数"})},{title:"转子条数",dataIndex:"generatorRotorBarCount",validateRules:l({type:"integer",title:"转子条数"})},{title:"定子槽",dataIndex:"generatorStatorSlotCount",validateRules:l({type:"integer",title:"定子槽"})}],tableData:[{key:"0"}]},{title:"",key:"3-1",dataIndex:"table3-1",addRowParentDataIndex:"generatorBearingList",tableData:[{key:"0",compType:"发电机轴承",bearingName:"驱动端1"},{key:"1",compType:"发电机轴承",bearingName:"驱动端2"},{key:"2",compType:"发电机轴承",bearingName:"非驱动端"}],columns:[{title:"名称",dataIndex:"compType",width:100,rowSpan:[0,3],showText:!0},{title:"位置",width:75,showText:!0,dataIndex:"bearingName"},{title:"轴承厂家",width:160,dataIndex:"bearingManufacturer"},{title:"轴承型号",dataIndex:"bearingModel",width:160},{title:"类型",dataIndex:"bearingType",inputType:"select",optionname:"zhuzhouType",width:110},{title:"节圈直径",subTitle:"两个滚子到中心的距离",dataIndex:"bearingPitchDiameter",width:180,validateRules:l({type:"number",title:"节圈直径"})},{title:"滚动体直径",dataIndex:"bearingRollDiameter",validateRules:l({type:"number",title:"滚动体直径"})},{title:"滚动体个数",dataIndex:"bearingRollCount",validateRules:l({type:"integer",title:"滚动体个数"})},{title:"接触角",dataIndex:"bearingContactAngle",validateRules:l({type:"number",title:"接触角"})}]}]}]},{title:"齿轮箱油液监测参数",dataIndex:"oil",key:"3-1",content:[{tableList:[{title:"",key:"3-1-1",dataIndex:"table3-11",columns:[{title:"润滑油厂商",dataIndex:"lubricantManufacturer",editable:!0,showsearch:!0},{title:"润滑油型号",dataIndex:"lubricantModel",editable:!0,showsearch:!0},{title:"40℃理论运动粘度(cst)",dataIndex:"lubricantViscosity40C",validateRules:l({type:"number",title:"40℃理论运动粘度"})},{title:"100℃理论运动粘度(cst)",dataIndex:"lubricantViscosity100C",validateRules:l({type:"number",title:"100℃理论运动粘度"})},{title:"25℃理论密度(kg/m³)",dataIndex:"lubricantDensity25C",validateRules:l({type:"number",title:"25℃理论密度"})}],tableData:[{key:"0"}]}]}]},{title:"齿轮箱",key:"4",dataIndex:"blade",addContentPathArr:[3],addContentParentDataIndex:"wtParametersGearboxes",content:[{key:"",tableList:[{title:"",key:"4-0",dataIndex:"table7",noParentDataIndex:!0,columns:[{title:"厂家",dataIndex:"gearboxManufacturer",optionname:"gearboxManufacturerOptions",inputType:"selectinput",hasChangeEvent:!0,isrequired:!0,formItemWidth:200,width:200},{title:"型号",dataIndex:"gearboxModel",optionname:"gearboxModelOptions",inputType:"selectinput",hasChangeEvent:!0,isrequired:!0,formItemWidth:200,width:200},{title:"传动比",dataIndex:"gearboxGearRatio",optionname:"gearboxGearRatioOptions",inputType:"selectinput",validateRules:l({type:"number",title:"传动比",required:!0}),formItemWidth:200,width:200}],tableData:[{key:"1"}]}]},{title:"第一级",key:"0",dataIndex:"stageNumber",selectForm:[{title:"传动结构",dataIndex:"stageType",optionname:"transmissionTtructure",hasChangeEvent:!0,isrequired:!0,inputType:"select",selectOptions:[{label:"行星级",value:"行星级"}],width:200}],tableList:k("行星级")},{title:"第二级",key:"1",dataIndex:"stageNumber",selectForm:[{title:"传动结构",dataIndex:"stageType",optionname:"transmissionTtructure",hasChangeEvent:!0,isrequired:!0,inputType:"select",selectOptions:[{label:"行星级",value:"行星级"},{label:"平行级",value:"平行级"}],width:200}],tableList:k("行星级")},{title:"第三级",key:"2",dataIndex:"stageNumber",selectForm:[{title:"传动结构",dataIndex:"stageType",optionname:"transmissionTtructure",hasChangeEvent:!0,inputType:"select",isrequired:!0,width:200,selectOptions:[{label:"行星级",value:"行星级"},{label:"平行级",value:"平行级"}]}],tableList:k("行星级")}]}];case"叶片":return[{title:"叶片振动",key:"1",dataIndex:"blade",content:[{tableList:[{title:"基本信息",key:"1-0",dataIndex:"table0",columns:[{title:"厂家",dataIndex:"bladeManufacturer"},{title:"型号",dataIndex:"bladeModel"},{title:"长度（mm）",dataIndex:"bladeLength",validateRules:l({type:"number",title:"长度"})},{title:"叶片材料",dataIndex:"bladeMaterial"},{title:"叶片质量(吨)",dataIndex:"bladeMass",validateRules:l({type:"number",title:"叶片质量"})},{title:"扫风面积(m²)",dataIndex:"sweptArea",validateRules:l({type:"number",title:"扫风面积"})}],tableData:[{key:"1"}]},{title:"固有频率",key:"1-1",dataIndex:"table1",columns:[{title:"方向",dataIndex:"bladeComponentDirection",showText:!0},{title:"理论一阶固有频率",dataIndex:"bladeFrequency1",dataIndexArr:["bladeEdgeFrequency1","bladeFlapFrequency1"],validateRules:l({type:"number",title:"理论一阶固有频率"})},{title:"理论二阶固有频率",dataIndex:"bladeFrequency2",dataIndexArr:["bladeEdgeFrequency2","bladeFlapFrequency2"],validateRules:l({type:"number",title:"理论二阶固有频率"})},{title:"理论三阶固有频率",dataIndex:"bladeFrequency3",dataIndexArr:["bladeEdgeFrequency3","bladeFlapFrequency3"],validateRules:l({type:"number",title:"理论三阶固有频率"})}],tableData:[{key:"1",bladeComponentDirection:"Edge-wise"},{key:"2",bladeComponentDirection:"Flap-wise"}]}]}]},{title:"变桨轴承",key:"2",dataIndex:"blade2",content:[{tableList:[{title:"",key:"2-0",dataIndex:"table3",columns:[{title:"变桨轴承类型",dataIndex:"bvstype",showText:!0},{title:"厂家",dataIndex:"pitchBearingManufacturer",dataIndexArr:["pitchBearingManufacturer1","pitchBearingManufacturer2","pitchBearingManufacturer3"]},{title:"型号",dataIndex:"pitchBearingModel",dataIndexArr:["pitchBearingModel1","pitchBearingModel2","pitchBearingModel3"]},{title:"变桨方式",inputType:"select",dataIndexArr:["pitchMethod","pitchMethod1","pitchMethod2"],optionname:"pitchMethod",rowSpan:[0,3]}],tableData:[{key:"1",bvstype:"变桨轴承1"},{key:"2",bvstype:"变桨轴承2"},{key:"3",bvstype:"变桨轴承3"}]},{title:"变桨轴承滚道参数",key:"2-1",dataIndex:"table4",columns:[{title:"滚道数量",dataIndex:"pitchBearingRollNumber",validateRules:l({type:"integer",title:"滚道数量"})},{title:"滚子类型",dataIndex:"pitchBearingRollType",editable:!0},{title:"滚子回转直径(mm)",dataIndex:"pitchBearingRollBackDiameter",validateRules:l({type:"number",title:"滚子回转直径"})},{title:"滚子直径(mm)",dataIndex:"pitchBearingRollDiameter",validateRules:l({type:"number",title:"滚子直径"})},{title:"滚子个数",dataIndex:"pitchBearingRollCount",validateRules:l({type:"integer",title:"滚子个数"})}],tableData:[{key:"1"}]},{title:"内、外圈参数",key:"2-2",dataIndex:"table5",columns:[{title:"参数类型",dataIndex:"pitchBearingType",showText:!0},{title:"外圈高度(mm)",dataIndex:"pitchBearingHeight",dataIndexArr:["pitchBearingInnerHeight","pitchBearingOuterHeight"],validateRules:l({type:"number",title:"外圈高度"})},{title:"内回转直径(mm)",dataIndex:"pitchBearingDiameter",dataIndexArr:["pitchBearingInnerDiameter","pitchBearingOuterDiameter"],validateRules:l({type:"number",title:"内回转直径"})},{title:"外回转直径(mm)",dataIndex:"pitchBearingDiameter",dataIndexArr:["pitchBearingInnerRollwayDiameter","pitchBearingOuterRollwayDiameter"],validateRules:l({type:"number",title:"外回转直径"})}],tableData:[{key:"1",pitchBearingType:"内圈"},{key:"2",pitchBearingType:"外圈"}]},{title:"齿圈参数",key:"2-3",dataIndex:"table6",columns:[{title:"模数",dataIndex:"gearCirclePitch",validateRules:l({type:"number",title:"模数"})},{title:"齿数",dataIndex:"gearCircleToothNumber",validateRules:l({type:"integer",title:"齿数"})},{title:"变位系数",dataIndex:"gearCircleProfileShiftFactor",validateRules:l({type:"number",title:"变位系数"})},{title:"齿顶圈直径(mm)",dataIndex:"gearCircleToothTipDiameter",validateRules:l({type:"number",title:"齿顶圈直径"})},{title:"轮齿长度(mm)",dataIndex:"gearCircleGearLength",validateRules:l({type:"number",title:"轮齿长度"})}],tableData:[{key:"1"}]}]}]},{title:"叶根螺栓",key:"3",dataIndex:"blade",content:[{tableList:[{title:"",key:"3-0",dataIndex:"table7",addRowParentDataIndex:"wtParametersFlanges",columns:[{title:"叶片法兰(风机主轴对应)",dataIndex:"name",showText:!0},{title:"螺栓厂家",dataIndex:"boltManufacturer",editable:!0},{title:"螺栓型号",dataIndex:"boltModel",editable:!0},{title:"螺栓长度(mm)",dataIndex:"boltLength",validateRules:l({type:"number",title:"螺栓长度"})},{title:"螺栓直径(mm)",dataIndex:"boltDiameter",validateRules:l({type:"number",title:"螺栓直径"})},{title:"螺栓数量",dataIndex:"boltCount",validateRules:l({type:"integer",title:"螺栓数量"})}],tableData:[{key:"1",name:"法兰一"},{key:"1",name:"法兰二"},{key:"1",name:"法兰三"}]}]}]}];case"塔筒":return[{title:"塔筒参数",key:"1",dataIndex:"tower",content:[{tableList:[{title:"基本信息",key:"1-0",dataIndex:"table0",columns:[{title:"塔筒类型",with:120,dataIndex:"towerType",inputType:"select",optionname:"towerType",hasChangeEvent:!0},{title:"塔基直径(mm)",dataIndex:"baseDiameter",validateRules:l({type:"number",title:"塔基直径"})},{title:"塔顶直径(mm)",dataIndex:"topDiameter",validateRules:l({type:"number",title:"塔顶直径"})},{title:"塔筒节数",dataIndex:"towerSectionCount",validateRules:l({type:"integer",title:"塔筒节数"})},{title:"叶轮并网转频(Hz)",dataIndex:"gridFrequency",validateRules:l({type:"number",title:"叶轮并网转频"})},{title:"叶轮最大转频(Hz)",dataIndex:"maxGridFrequency",validateRules:l({type:"number",title:"叶轮最大转频"})}],tableData:[{key:"1"}]}]}]},{title:"塔筒螺栓预紧力监测参数",key:"2",dataIndex:"2",content:[{tableList:[{title:"塔筒固有频率参数",dataIndex:"table2",columns:[{title:"一阶固有频率设计上限",dataIndex:"towerNaturalFrequencyUpper",with:75,validateRules:l({type:"number",title:"一阶固有频率设计上限"})},{title:"一阶固有频率设计下限",dataIndex:"towerNaturalFrequencyLower",validateRules:l({type:"number",title:"一阶固有频率设计下限"})},{title:"理论一阶固有频率",dataIndex:"towerNaturalFrequency",validateRules:l({type:"number",title:"理论一阶固有频率"})},{title:"理论二阶固有频率",dataIndex:"secondOrderNaturalFrequency",validateRules:l({type:"number",title:"理论二阶固有频率"})}],tableData:[{key:"1"}]},{title:"塔筒挠度系数",dataIndex:"table2",columns:[{title:"挠度系数",dataIndex:"deflectionCoefficient",validateRules:l({type:"number",title:"挠度系数"})}],tableData:[{key:"1"}]}]}]},{title:"塔筒倾覆监测参数",key:"3",dataIndex:"3",content:[{tableList:[{title:"",key:"3-0",dataIndex:"table3",addRow:[2,0,0],addRowParentDataIndex:"wtParametersFlanges",columns:[{title:"法兰层级",subTitle:"自底向上",dataIndex:"flangeLevel",optionname:"flangeLevel",inputType:"select",with:75,isrequired:!0},{title:"螺栓厂家",dataIndex:"boltManufacturer"},{title:"单法兰螺栓数量",dataIndex:"boltCount",validateRules:l({type:"integer",title:"单法兰螺栓数量"})},{title:"螺栓型号",dataIndex:"boltModel"},{title:"螺栓长度(mm)",dataIndex:"boltLength",validateRules:l({type:"number",title:"螺栓长度"})},{title:"螺栓直径(mm)",dataIndex:"boltDiameter",validateRules:l({type:"number",title:"螺栓直径"})}],tableData:[{key:"1"}]}]}]},{title:"塔筒索力监测",key:"4",dataIndex:"4",content:[{tableList:[{title:"",key:"4-1",dataIndex:"table4",columns:[{title:"拉索簇数",dataIndex:"cableClusterCount",validateRules:l({type:"integer",title:"拉索簇数"})},{title:"每簇根数",dataIndex:"cablePerCluster",editable:!0,validateRules:l({type:"integer",title:"每簇根数"})},{title:"每根钢丝条数",dataIndex:"wirePerCable",editable:!0,validateRules:l({type:"integer",title:"每根钢丝条数"})},{title:"每条钢丝直径(mm)",dataIndex:"wireDiameter",editable:!0,validateRules:l({type:"number",title:"每条钢丝直径"})},{title:"拉索总长度(m)",dataIndex:"cableLength",validateRules:l({type:"number",title:"拉索总长度"})}],tableData:[{key:"1"}]},{title:"",key:"4-2",dataIndex:"table5",columns:[{title:"是否有换向器或者摩擦片",dataIndex:"hasSwitch",inputType:"select",optionname:"isCommutatorOrFrictionPlate"},{title:"换向器或者摩擦片高度(m)",dataIndex:"switchHeight",validateRules:l({type:"number",title:"换向器或者摩擦片高度"})}],tableData:[{key:"1"}]}]}]}];default:return[]}},B=(v,m,D)=>{if(v=="2"){if(m=="平行级")return k("level2");if(m=="行星级")return k("行星级")}else if(v=="3"){if(m=="平行级"){if(D=="平行级")return k("px-level3");if(D=="行星级")return k("行星级")}else if(m=="行星级"){if(D=="平行级")return k("xx-level3");if(D=="行星级")return k("行星级")}}else if(v>3){if(m=="平行级")return k("平行级");if(m=="行星级")return k("行星级")}},q=()=>[{title:"种类",dataIndex:"bearingName",align:"left",showText:!0,width:200},{title:"厂家",dataIndex:"bearingManufacturer",width:160},{title:"型号",dataIndex:"bearingModel",width:160},{title:"类型",dataIndex:"bearingType",inputType:"select",optionname:"zhuzhouType",width:110},{title:"节圈直径",subTitle:"两个滚子到中心的距离",dataIndex:"bearingPitchDiameter",width:180,validateRules:l({type:"number",title:"节圈直径"})},{title:"滚动体直径",dataIndex:"bearingRollDiameter",validateRules:l({type:"number",title:"滚动体直径"})},{title:"滚动体个数",dataIndex:"bearingRollCount",validateRules:l({type:"integer",title:"滚动体个数"})},{title:"接触角",dataIndex:"bearingContactAngle",validateRules:l({type:"number",title:"接触角"})}],k=v=>({行星级:[{title:"齿轮参数",dataIndex:"table7",columns:[{title:"行星轮个数",dataIndex:"planetCount",validateRules:l({type:"integer",title:"行星轮个数"})},{title:"太阳轮个数",dataIndex:"sunTeethCount",validateRules:l({type:"integer",title:"太阳轮个数"})},{title:"行星轮齿数",dataIndex:"planetTeethCount",validateRules:l({type:"integer",title:"行星轮齿数"})},{title:"内齿圈齿数",dataIndex:"ringTeethCount",validateRules:l({type:"integer",title:"内齿圈齿数"})}],tableData:[{key:"1"}]},{title:"轴承参数",dataIndex:"table8",columns:q(),addRowParentDataIndex:"bearingList",tableData:[{key:"1",bearingName:"行星架前轴承"},{key:"2",bearingName:"行星架后轴承"},{key:"3",bearingName:"行星轮轴承"}]}],level2:[{title:"齿轮参数",key:"3-0",dataIndex:"table7",columns:[{title:"大齿轮齿数",dataIndex:"largeTeethCount",validateRules:l({type:"integer",title:"大齿轮齿数"})},{title:"小齿轮齿数",dataIndex:"smallTeethCount",validateRules:l({type:"integer",title:"小齿轮齿数"})}],tableData:[{key:"1"}]},{title:"轴承参数",dataIndex:"table8",addRowParentDataIndex:"bearingList",columns:q(),tableData:[{key:"1",bearingName:"低速轴前轴承"},{key:"2",bearingName:"低速轴后轴承1(风轮端)"},{key:"3",bearingName:"低速轴后轴承2(电机端)"},{key:"4",bearingName:"中速轴前轴承"},{key:"5",bearingName:"中速轴后轴承1(风轮端)"},{key:"6",bearingName:"中速轴后轴承2(电机端)"}]}],"xx-level3":[{title:"齿轮参数",dataIndex:"table7",columns:[{title:"大齿轮齿数",dataIndex:"largeTeethCount",validateRules:l({type:"integer",title:"大齿轮齿数"})},{title:"小齿轮齿数",dataIndex:"smallTeethCount",validateRules:l({type:"integer",title:"小齿轮齿数"})}],tableData:[{key:"1"}]},{title:"轴承参数",dataIndex:"table8",addRowParentDataIndex:"bearingList",columns:q(),tableData:[{key:"1",bearingName:"低速轴前轴承"},{key:"2",bearingName:"低速轴后轴承1(风轮端)"},{key:"3",bearingName:"低速轴后轴承2(电机端)"},{key:"4",bearingName:"高速轴前轴承"},{key:"5",bearingName:"高速轴后轴承1(风轮端)"},{key:"6",bearingName:"高速轴后轴承2(电机端)"}]}],"px-level3":[{title:"齿轮参数",dataIndex:"table7",columns:[{title:"大齿轮齿数",dataIndex:"largeTeethCount",validateRules:l({type:"integer",title:"大齿轮齿数"})},{title:"小齿轮齿数",dataIndex:"smallTeethCount",validateRules:l({type:"integer",title:"小齿轮齿数"})}],tableData:[{key:"1"}]},{title:"轴承参数",dataIndex:"table8",addRowParentDataIndex:"bearingList",columns:q(),tableData:[{key:"4",bearingName:"高速轴前轴承"},{key:"5",bearingName:"高速轴后轴承1(风轮端)"},{key:"6",bearingName:"高速轴后轴承2(电机端)"}]}],平行级:[{title:"齿轮参数",dataIndex:"table7",columns:[{title:"大齿轮齿数",dataIndex:"largeTeethCount",validateRules:l({type:"integer",title:"大齿轮齿数"})},{title:"小齿轮齿数",dataIndex:"smallTeethCount",validateRules:l({type:"integer",title:"小齿轮齿数"})}],tableData:[{key:"1"}]},{title:"轴承参数",dataIndex:"table9",addRowParentDataIndex:"bearingList",columns:q(),tableData:[{key:"1",bearingName:"低速轴前轴承"},{key:"2",bearingName:"低速轴后轴承1(风轮端)"},{key:"3",bearingName:"低速轴后轴承2(电机端)"},{key:"4",bearingName:"中速轴前轴承"},{key:"5",bearingName:"中速轴后轴承1(风轮端)"},{key:"6",bearingName:"中速轴后轴承2(电机端)"},{key:"7",bearingName:"高速轴前轴承"},{key:"8",bearingName:"高速轴后轴承1(风轮端)"},{key:"9",bearingName:"高速轴后轴承2(电机端)"}]}]})[v],Xe=["onClick"],Je=["onClick"],Qe=["onClick"],Ye={key:2},Ze={class:"modal-content"},et={class:"partTitle"},tt={class:"partContent"},at={key:0,class:"contextTitle"},lt=["onClick"],nt={class:"formGroup"},it={class:"label"},rt={class:"formItem"},dt={class:"partItem"},ot={key:1,class:"addOperate"},st=["onClick"],ut={key:0,class:"addOperate"},ct=["onClick"],mt={class:ie(["footer-btn"])},pt={class:"btnitem"},bt={class:"btnitem"},yt={key:3},gt={__name:"index",setup(v){Ge();const m=qe(),D=T(null),O=T(!1),F=T(""),R=T(""),j=T({}),w=T({}),K=T([]),S=T([]),g=T([]),X=T({}),J=T({}),Q=T(!1),me=Ne(),u=_e({paramsId:"",chinaNumber:{1:"一",2:"二",3:"三",4:"四",5:"五",6:"六",7:"七",8:"八",9:"九",10:"十"},modelId:"",modalOptions:je(),totalPages:0,currentPage:1,itemsPerPage:10,paginatedData:[],gbxL2:"行星级",gbxL3:"行星级",paramsType:""}),re=T([]),Y=async()=>{(!m.modelStructureType||m.modelStructureType.length<1)&&await m.fetchGetModelStructureType()},W=async()=>{O.value=!0,await Y(),K.value=await m.fetchModellist(),O.value=!1,re.value=ne({structureTypes:m.modelStructureType})};Me(()=>me.params.id,i=>{K.value.length===0&&W()},{immediate:!0});const pe=Pe(()=>R.value==="batchAdd"||R.value==="component"?"1200px":"600px"),be=async i=>{const{title:e,operateType:t,tableKey:a}=i;R.value=t,F.value="批量增加机型",await Y(),S.value=ne({structureTypes:m.modelStructureType},se),ae()},ye=async(i={})=>{const{selectedkeys:e}=i;if(e.length<1){P.error("请选择要删除的行");return}const t=await m.fetchBatchDelTrubineModel(e);t&&t.code===1?(W(),_(),P.success("提交成功")):P.error("提交失败:"+t.msg)},A=({istable:i,column:e,tableItem:t,index:a,partItem:n,tablesIndex:d,formitem:o})=>{if(i){let s="";return n.addContentParentDataIndex&&!t.noParentDataIndex&&d>0?s=n.addContentParentDataIndex+"&&"+(d-1)+"&&":s="",e.dataIndexArr?s+=e.dataIndexArr[a]:t.addRowParentDataIndex?s+=t.addRowParentDataIndex+"&&"+a+"&&"+e.dataIndex:s+=e.dataIndex,{...e,isdisplay:!e.showText,widthAuto:!0,selectOptions:e.optionname?u.modalOptions[e.optionname]:[],dataIndex:s}}else{let s=`${n.addContentParentDataIndex?n.addContentParentDataIndex+"&&"+(d-1)+"&&":""}${o.dataIndex}`;return{...o,formItemWidth:200,dataIndex:s}}},ge=async i=>{const{rowData:e,title:t,operateType:a,tableKey:n}=i;R.value=a,j.value=e,F.value="编辑机型基本信息",await Y(),S.value=ne({structureTypes:m.modelStructureType,isEdit:!0},se),ae()},Z=async(i,e)=>{O.value=!0,R.value="component",F.value="编辑"+i;let t=[...Ke(i)];u.modelId=e.turbineModel,u.paramsType=i;let a={};if(i=="机舱"?(a=await m.fetchWTParamGetWtNaceller({id:e.turbineModel}),u.paramsId="nacelleId"):i=="叶片"?(a=await m.fetchWTParamGetWtBlade({id:e.turbineModel}),u.paramsId="rotorId"):i=="塔筒"&&(a=await m.fetchWTParamGetWtTower({id:e.turbineModel}),u.paramsId="towerId"),a){try{for(const n in a){if(Array.isArray(a[n])&&a[n].length){for(let d=0;d<a[n].length;d++){if(i=="机舱"&&n=="wtParametersGearboxes"){if(d==1){let o=B(2,a[n][d].stageType);t[3].content[d+1].tableList=[...o]}else if(d==2){let o=B(3,a[n][d-1].stageType,a[n][d].stageType);t[3].content[d+1].tableList=[...o]}}i=="机舱"&&!t[3].content[d+1]&&t[3].content.push({title:"第",key:"",dataIndex:"stageNumber",selectForm:[{title:"传动结构",dataIndex:"stageType",optionname:"transmissionTtructure",isrequired:!0,hasChangeEvent:!0,inputType:"select",options:[{text:"行星级",value:"行星级"},{text:"平行级",value:"平行级"}]}],tableList:k(a[n][d].stageType)});for(const o in a[n][d])if(Array.isArray(a[n][d][o])&&a[n][d][o].length){i=="机舱"&&(t[3].content[d+1].tableList[1].tableData=a[n][d][o],t[3].content[d+1].tableList[1].loopTableDatas=!0);for(let s=0;s<a[n][d][o].length;s++)for(const r in a[n][d][o][s])a[n+"&&"+d+"&&"+o+"&&"+s+"&&"+r]=a[n][d][o][s][r]}else a[n+"&&"+d+"&&"+o]=a[n][d][o]}i=="塔筒"&&1<a[n].length&&(t[2].content[0].tableList[0].tableData=a[n],t[2].content[0].tableList[0].loopTableDatas=!0)}i=="塔筒"&&n=="hasSwitch"&&(a[n]=a[n]?"true":"false")}}catch(n){console.log(n)}i=="塔筒"&&a.towerType&&a.towerType=="混塔"&&(t[0].content[0].tableList[0].columns=t[0].content[0].tableList[0].columns.concat(de())),g.value=[...t],w.value=a}if(i=="机舱"){let n=await m.fetchGetGearboxModels();u.modalOptions={...u.modalOptions,gearboxManufacturerOptions:n}}O.value=!1,ae()},de=()=>[{title:"混凝土塔筒高度",dataIndex:"concreteTowerHeight"},{title:"钢制塔筒高度",dataIndex:"steelTowerHeight"}],he=(i,e)=>{let t=[...g.value],a={key:"1"};if(e=="wtParametersFlanges"){let n=t[i[0]].content[i[1]].tableList[i[2]].tableData.length;n=n+1,a.name=`${u.chinaNumber[n]}层法兰`}t[i[0]].content[i[1]].tableList[i[2]].tableData.push(a),g.value=t},xe=async i=>{let e={...i,windTurbineModelName:i.turbineModel,ratedPower:i.ratedPower};const t=await m.fetchUpdateTurbineModel(e);t&&t.code===1?(W(),_(),P.success("提交成功")):P.error("提交失败:"+t.msg)},ve=async i=>{let e=Ve(i);e=e.map(a=>({...a,factedPower:a.ratedPower,windTurbineModelName:a.turbineModel}));let t=await m.fetchAddWindModel(e);t&&t.code===1?(W(),_(),P.success("提交成功")):P.error("提交失败:"+t.msg)},ee=(i,e)=>{w.value[i]=e},oe=i=>{const e=i.value;if(!e||e=="")return;switch(i.dataIndex){case"gearboxManufacturer":const n=u.modalOptions.gearboxManufacturerOptions.filter(r=>r.value==e);if(n&&n.length&&n[0].values&&n[0].values.length){let r=[];for(let c=0;c<n[0].values.length;c++)r.push({text:n[0].values[c].key,value:n[0].values[c].key,values:n[0].values[c].values});u.modalOptions.gearboxModelOptions=r,u.modalOptions={...u.modalOptions}}ee("gearboxModel",""),ee("gearboxGearRatio","");break;case"gearboxModel":const o=u.modalOptions.gearboxModelOptions.filter(r=>r.value==e);if(o&&o.length&&o[0].values&&o[0].values.length){let r=[];for(let c=0;c<o[0].values.length;c++)r.push({label:o[0].values[c].key,value:o[0].values[c].key});u.modalOptions.gearboxGearRatioOptions=r,u.modalOptions={...u.modalOptions}}ee("gearboxGearRatio","");break;case"towerType":let s=[...g.value];e=="混塔"?s[0].content[0].tableList[0].columns=s[0].content[0].tableList[0].columns.concat(de()):s[0].content[0].tableList[0].columns.length>7&&s[0].content[0].tableList[0].columns.splice(7,2),g.value=[...s];break}},fe=(i,e,t)=>{const a=i.value;let n=g.value;if(t==2){u.gbxL2=a;let d=B(t,a);n[3].content[t].tableList=[...d];let s=w.value["wtParametersGearboxes&&2&&stageType"];if(!s||s!=="行星级"){let r=B(3,a,u.gbxL3);n[3].content[t+1].tableList=[...r],$(r,`wtParametersGearboxes&&${t}`)}g.value=n,$(d,`wtParametersGearboxes&&${t-1}`)}else if(t==3){u.gbxL3=a;let d=B(t,u.gbxL2,a);n[3].content[t].tableList=[...d],g.value=n,$(d,`wtParametersGearboxes&&${t-1}`)}else if(t>3){let d=B(t,a);n[3].content[t].tableList=[...d],g.value=n,$(d,`wtParametersGearboxes&&${t-1}`)}},$=(i,e)=>{const t={...w.value};for(const a in t)a.startsWith(e)&&a.indexOf("stageId")===-1&&a.indexOf("stageNumber")===-1&&a.indexOf("stageType")===-1&&delete t[a];for(let a=0;a<i.length;a++){const n=i[a].tableData;for(let d=0;d<n.length;d++){const o=n[d];for(const s in o)if(s!=="key")if(i[a].addRowParentDataIndex){let r=`${e}&&${i[a].addRowParentDataIndex}&&${d}&&${s}`;t[r]=o[s]}else t[`${e}&&${s}`]=o[s]}}w.value={...t}},Ie=i=>{let e=g.value[i[0]].content.length,t=k("行星级");g.value[i[0]].content.push({title:"第",key:e,dataIndex:"table"+e,selectForm:[{title:"传动结构",dataIndex:"stageType",optionname:"transmissionTtructure",inputType:"select",isrequired:!0,hasChangeEvent:!0,options:[{text:"行星级",value:"行星级"},{text:"平行级",value:"平行级"}]}],tableList:t}),g.value=[...g.value];let a=`wtParametersGearboxes&&${e-1}&&`,n={...w.value,[a+"stageType"]:"行星级",[a+"stageNumber"]:e};for(let d=0;d<t[1].tableData.length;d++)n[a+`bearingList&&${d}&&bearingName`]=t[1].tableData[d].bearingName;w.value=n},we=(i,e)=>{g.value[i[0]].content.splice(e,1),g.value=[...g.value]},te=async(i,e,t)=>{w.value[i]=e,t&&(t.isrequired||t.validateRules)&&setTimeout(()=>{D.value.validateFields([i])},100)},Te=async i=>{const e={};try{Object.keys(i).forEach(a=>{let n=a.split("_"),d=i[a],o=d;if(d=="true"?o=!0:d=="false"&&(o=!1),n[0].indexOf("&&")>-1){let s=n[0].split("&&");if(s.length>3){let r=s[0],c=s[1],p=s[2],b=s[3]*1,y=s[4];if(e[r]?e[r].length>c||e[r].push({}):(e[r]=[],e[r].push({})),e[r][c][p])e[r][c][p].length>b||e[r][c][p].push({}),e[r][c][p][b][y]=o;else{if(e[r][c][p]=[],b>e[r][c][p].length-1)for(let E=0;E<b+1;E++)e[r][c][p].push({});e[r][c][p][b][y]=o}}else if(s.length>0){let r=s[0],c=s[1],p=s[2];c==""||c=="-1"?e[p]=o:e[r]?(e[r].length>c||e[r].push({}),e[r][c][p]=o):(e[r]=[],e[r].push({}),e[r][c][p]=o)}else e[n[0]]=o}else e[n[0]]=o})}catch(a){console.log(a)}e.modelId=u.modelId,e[u.paramsId]=w.value[u.paramsId];let t={};u.paramsType=="机舱"?t=await m.fetchAddOrUpdateWtNacelleParameter(e):u.paramsType=="叶片"?t=await m.fetchAddOrUpdateWtBladeParameter(e):u.paramsType=="塔筒"&&(t=await m.fetchAddOrUpdateWtTowerParameter(e)),t&&t.code===1&&(_(),P.success("提交成功")),_()},ae=()=>{Q.value=!0},_=i=>{Q.value=!1,S.value=[],j.value={},w.value={},g.value=[],u.modelId="",u.paramsType=""},se=async(i,e)=>{if(!i)return;let t=null,a="ratedGeneratorSpeed",n="gridConnectedGeneratorSpeed";if(R.value==="edit")t=X.value.getFieldsValue();else{t=J.value.getTableFieldsValue();let r=i.field.match(/\d+/)[0];a=`ratedGeneratorSpeed[${r}]`,n=`gridConnectedGeneratorSpeed[${r}]`}if(!t)return;const d=parseFloat(t[a]),o=parseFloat(t[n]);if(d!==void 0&&o!==void 0&&d<o)return Promise.reject(new Error("发电机并网转速不能小于发电机额定转速"));let s=i.field==a?o:a;return R.value==="edit"?X.value.clearValidate(s):J.value.clearValidate(s),Promise.resolve()};return(i,e)=>{const t=Ce,a=Be,n=ze,d=Ee,o=$e,s=Ae;return h(),le(s,{spinning:O.value,size:"large"},{default:M(()=>[x("div",null,[C(Le,{ref:"table",size:"default","table-key":"0","table-title":"机型列表","table-columns":re.value,noBatchApply:!0,"table-operate":["edit","delete","add","batchDelete","batchAdd"],"record-key":"turbineModel","table-datas":K.value,onAddRow:be,onDeleteRow:ye,onEditRow:ge},{otherColumn:M(({column:r,record:c,text:p})=>[x("span",{onClick:b=>Z("机舱",c),class:"editBtn"},"机舱",8,Xe),x("span",{onClick:b=>Z("叶片",c),class:"editBtn"},"叶片",8,Je),x("span",{onClick:b=>Z("塔筒",c),class:"editBtn"},"塔筒",8,Qe)]),_:1},8,["table-columns","table-datas"])]),C(o,{width:pe.value,open:Q.value,title:F.value,footer:"",onCancel:_,maskClosable:!1,destroyOnClose:!0},{default:M(()=>[R.value==="edit"?(h(),le(De,{key:0,ref_key:"modalFormRef",ref:X,titleCol:S.value,initFormData:j.value,onSubmit:xe},null,8,["titleCol","initFormData"])):R.value==="batchAdd"?(h(),le(Se,{key:1,ref_key:"tableFormRef",ref:J,size:"default","table-key":"0","table-columns":S.value,"table-operate":["copyUp","delete"],"table-datas":[],"noCopyUp-keys":["turbineModel"],onSubmit:ve,onCancel:_},null,8,["table-columns"])):R.value==="component"?(h(),I("div",Ye,[C(d,{onFinish:Te,autocomplete:"off",model:w.value,ref_key:"componentFormRef",ref:D},{default:M(()=>[x("div",Ze,[(h(!0),I(V,null,U(g.value,(r,c)=>(h(),I("div",{class:"partBox",key:r.title},[x("h5",et,[x("span",null,L(r.title),1)]),x("div",tt,[(h(!0),I(V,null,U(r.content,(p,b)=>(h(),I(V,null,[p.title?(h(),I("h5",at,[H(L(p.title=="第"?u.chinaNumber[b]?"第"+u.chinaNumber[b]+"级":"第"+b+"级":p.title)+" ",1),b>3?(h(),I("img",{key:0,title:"删除",onClick:y=>we(r.addContentPathArr,b),class:"deleteBtn",src:Ue},null,8,lt)):G("",!0),C(t,{"item-props":{...p,isdisplay:!1,dataIndex:`${r.addContentParentDataIndex?r.addContentParentDataIndex+"&&"+(b-1)+"&&":""}stageNumber`},onOnchangeSelect:oe,modelValue:w.value[`${r.addContentParentDataIndex?r.addContentParentDataIndex+"&&"+(b-1)+"&&":""}stageNumber`],"onUpdate:modelValue":y=>te(`${r.addContentParentDataIndex?r.addContentParentDataIndex+"&&"+(b-1)+"&&":""}stageNumber`,y)},null,8,["item-props","modelValue","onUpdate:modelValue"])])):G("",!0),x("div",{class:ie(p.title?"contextBox":"")},[(h(!0),I(V,null,U(p.selectForm,(y,E)=>(h(),I("div",nt,[x("label",it,L(y.title)+":",1),x("div",rt,[C(t,{notshowLabels:!0,"item-props":A({formitem:y,tableItem:p,partItem:r,tablesIndex:b}),onOnchangeSelect:(f,ue)=>fe(f,ue,b),modelValue:w.value[A({formitem:y,tableItem:p,partItem:r,tablesIndex:b}).dataIndex],"onUpdate:modelValue":f=>te(A({formitem:y,tableItem:p,partItem:r,tablesIndex:b}).dataIndex,f)},null,8,["item-props","onOnchangeSelect","modelValue","onUpdate:modelValue"])])]))),256)),x("div",{class:ie(p.selectForm?"contentBorder":"")},[(h(!0),I(V,null,U(p.tableList,(y,E)=>(h(),I("div",dt,[y&&y.title?(h(),I("h6",{class:"tableTitle",key:y.dataIndex},L(y.title),1)):G("",!0),C(a,{bordered:"",columns:y.columns,"data-source":y.tableData,pagination:!1,size:"small"},{headerCell:M(({column:f})=>[H(L(f.title),1)]),bodyCell:M(({column:f,record:ue,text:ke,index:z})=>[C(t,{modelValue:w.value[A({istable:!0,column:f,tableItem:y,index:z,partItem:r,tablesIndex:b}).dataIndex],notshowLabels:!0,"item-props":A({istable:!0,column:f,tableItem:y,index:z,partItem:r,tablesIndex:b}),onOnchangeSelect:oe,"onUpdate:modelValue":Re=>te(`${r.addContentParentDataIndex&&b>0?r.addContentParentDataIndex+"&&"+(b-1)+"&&":""}${f.dataIndexArr?f.dataIndexArr[z]:y.addRowParentDataIndex?y.addRowParentDataIndex+"&&"+z+"&&"+f.dataIndex:f.dataIndex}`,Re,f)},null,8,["modelValue","item-props","onUpdate:modelValue"]),Oe(x("span",null,L(ke),513),[[Fe,f.showText]])]),_:2},1032,["columns","data-source"]),y.addRow&&y.tableData.length<10?(h(),I("div",ot,[x("img",{title:"添加一行",src:ce,onClick:f=>he(y.addRow,y.addRowParentDataIndex)},null,8,st)])):G("",!0)]))),256))],2)],2)],64))),256)),r.addContentPathArr?(h(),I("div",ut,[x("img",{title:"添加一级",src:ce,onClick:p=>Ie(r.addContentPathArr)},null,8,ct)])):G("",!0)])]))),128))]),x("div",mt,[x("div",pt,[C(n,{onClick:_},{default:M(()=>e[0]||(e[0]=[H("取消",-1)])),_:1,__:[0]})]),x("div",bt,[C(n,{type:"primary","html-type":"submit"},{default:M(()=>e[1]||(e[1]=[H("确定",-1)])),_:1,__:[1]})])])]),_:1},8,["model"])])):(h(),I("div",yt))]),_:1},8,["width","open","title"])]),_:1},8,["spinning"])}}},At=We(gt,[["__scopeId","data-v-2ace92c6"]]);export{At as default};
