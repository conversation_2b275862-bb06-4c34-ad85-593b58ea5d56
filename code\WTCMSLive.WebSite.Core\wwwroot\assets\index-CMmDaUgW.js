import{b as i,ag as q,r as O,F as J,ch as K,ci as Y,cj as k,a0 as d,Z as ee,_ as te,a1 as le,aL as ne,a4 as Q,a5 as B,a7 as oe,k as ie,ae,aR as se,aM as F,j as re,aa as G,aQ as ce}from"./index-DLKlSkMo.js";import{u as de,r as X,a as pe}from"./styleChecker-tOiS7TSB.js";function I(e){return e!=null}const E=e=>{const{itemPrefixCls:t,component:l,span:o,labelStyle:n,contentStyle:a,bordered:c,label:r,content:s,colon:u}=e,p=l;return c?i(p,{class:[{[`${t}-item-label`]:I(r),[`${t}-item-content`]:I(s)}],colSpan:o},{default:()=>[I(r)&&i("span",{style:n},[r]),I(s)&&i("span",{style:a},[s])]}):i(p,{class:[`${t}-item`],colSpan:o},{default:()=>[i("div",{class:`${t}-item-container`},[(r||r===0)&&i("span",{class:[`${t}-item-label`,{[`${t}-item-no-colon`]:!u}],style:n},[r]),(s||s===0)&&i("span",{class:`${t}-item-content`,style:a},[s])])]})},ue=e=>{const t=(u,p,L)=>{let{colon:m,prefixCls:x,bordered:b}=p,{component:y,type:w,showLabel:M,showContent:P,labelStyle:g,contentStyle:S}=L;return u.map((f,h)=>{var $,v;const j=f.props||{},{prefixCls:_=x,span:R=1,labelStyle:A=j["label-style"],contentStyle:H=j["content-style"],label:W=(v=($=f.children)===null||$===void 0?void 0:$.label)===null||v===void 0?void 0:v.call($)}=j,N=K(f),z=Y(f),D=k(f),{key:T}=f;return typeof y=="string"?i(E,{key:`${w}-${String(T)||h}`,class:z,style:D,labelStyle:d(d({},g),A),contentStyle:d(d({},S),H),span:R,colon:m,component:y,itemPrefixCls:_,bordered:b,label:M?W:null,content:P?N:null},null):[i(E,{key:`label-${String(T)||h}`,class:z,style:d(d(d({},g),D),A),span:1,colon:m,component:y[0],itemPrefixCls:_,bordered:b,label:W},null),i(E,{key:`content-${String(T)||h}`,class:z,style:d(d(d({},S),D),H),span:R*2-1,component:y[1],itemPrefixCls:_,bordered:b,content:N},null)]})},{prefixCls:l,vertical:o,row:n,index:a,bordered:c}=e,{labelStyle:r,contentStyle:s}=q(Z,{labelStyle:O({}),contentStyle:O({})});return o?i(J,null,[i("tr",{key:`label-${a}`,class:`${l}-row`},[t(n,e,{component:"th",type:"label",showLabel:!0,labelStyle:r.value,contentStyle:s.value})]),i("tr",{key:`content-${a}`,class:`${l}-row`},[t(n,e,{component:"td",type:"content",showContent:!0,labelStyle:r.value,contentStyle:s.value})])]):i("tr",{key:a,class:`${l}-row`},[t(n,e,{component:c?["th","td"]:"td",type:"item",showLabel:!0,showContent:!0,labelStyle:r.value,contentStyle:s.value})])},me=e=>{const{componentCls:t,descriptionsSmallPadding:l,descriptionsDefaultPadding:o,descriptionsMiddlePadding:n,descriptionsBg:a}=e;return{[`&${t}-bordered`]:{[`${t}-view`]:{border:`${e.lineWidth}px ${e.lineType} ${e.colorSplit}`,"> table":{tableLayout:"auto",borderCollapse:"collapse"}},[`${t}-item-label, ${t}-item-content`]:{padding:o,borderInlineEnd:`${e.lineWidth}px ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderInlineEnd:"none"}},[`${t}-item-label`]:{backgroundColor:a,"&::after":{display:"none"}},[`${t}-row`]:{borderBottom:`${e.lineWidth}px ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderBottom:"none"}},[`&${t}-middle`]:{[`${t}-item-label, ${t}-item-content`]:{padding:n}},[`&${t}-small`]:{[`${t}-item-label, ${t}-item-content`]:{padding:l}}}}},fe=e=>{const{componentCls:t,descriptionsExtraColor:l,descriptionItemPaddingBottom:o,descriptionsItemLabelColonMarginRight:n,descriptionsItemLabelColonMarginLeft:a,descriptionsTitleMarginBottom:c}=e;return{[t]:d(d(d({},le(e)),me(e)),{"&-rtl":{direction:"rtl"},[`${t}-header`]:{display:"flex",alignItems:"center",marginBottom:c},[`${t}-title`]:d(d({},ne),{flex:"auto",color:e.colorText,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}),[`${t}-extra`]:{marginInlineStart:"auto",color:l,fontSize:e.fontSize},[`${t}-view`]:{width:"100%",borderRadius:e.borderRadiusLG,table:{width:"100%",tableLayout:"fixed"}},[`${t}-row`]:{"> th, > td":{paddingBottom:o},"&:last-child":{borderBottom:"none"}},[`${t}-item-label`]:{color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"start","&::after":{content:'":"',position:"relative",top:-.5,marginInline:`${a}px ${n}px`},[`&${t}-item-no-colon::after`]:{content:'""'}},[`${t}-item-no-label`]:{"&::after":{margin:0,content:'""'}},[`${t}-item-content`]:{display:"table-cell",flex:1,color:e.colorText,fontSize:e.fontSize,lineHeight:e.lineHeight,wordBreak:"break-word",overflowWrap:"break-word"},[`${t}-item`]:{paddingBottom:0,verticalAlign:"top","&-container":{display:"flex",[`${t}-item-label`]:{display:"inline-flex",alignItems:"baseline"},[`${t}-item-content`]:{display:"inline-flex",alignItems:"baseline"}}},"&-middle":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingSM}}},"&-small":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingXS}}}})}},be=ee("Descriptions",e=>{const t=e.colorFillAlter,l=e.fontSizeSM*e.lineHeightSM,o=e.colorText,n=`${e.paddingXS}px ${e.padding}px`,a=`${e.padding}px ${e.paddingLG}px`,c=`${e.paddingSM}px ${e.paddingLG}px`,r=e.padding,s=e.marginXS,u=e.marginXXS/2,p=te(e,{descriptionsBg:t,descriptionsTitleMarginBottom:l,descriptionsExtraColor:o,descriptionItemPaddingBottom:r,descriptionsSmallPadding:n,descriptionsDefaultPadding:a,descriptionsMiddlePadding:c,descriptionsItemLabelColonMarginRight:s,descriptionsItemLabelColonMarginLeft:u});return[fe(p)]});B.any;const ye=()=>({prefixCls:String,label:B.any,labelStyle:{type:Object,default:void 0},contentStyle:{type:Object,default:void 0},span:{type:Number,default:1}}),ge=Q({compatConfig:{MODE:3},name:"ADescriptionsItem",props:ye(),setup(e,t){let{slots:l}=t;return()=>{var o;return(o=l.default)===null||o===void 0?void 0:o.call(l)}}}),V={xxxl:3,xxl:3,xl:3,lg:3,md:3,sm:2,xs:1};function Se(e,t){if(typeof e=="number")return e;if(typeof e=="object")for(let l=0;l<X.length;l++){const o=X[l];if(t[o]&&e[o]!==void 0)return e[o]||V[o]}return 3}function U(e,t,l){let o=e;return(l===void 0||l>t)&&(o=pe(e,{span:t})),o}function $e(e,t){const l=ce(e),o=[];let n=[],a=t;return l.forEach((c,r)=>{var s;const u=(s=c.props)===null||s===void 0?void 0:s.span,p=u||1;if(r===l.length-1){n.push(U(c,a,u)),o.push(n);return}p<a?(a-=p,n.push(c)):(n.push(U(c,a,p)),o.push(n),a=t,n=[])}),o}const ve=()=>({prefixCls:String,bordered:{type:Boolean,default:void 0},size:{type:String,default:"default"},title:B.any,extra:B.any,column:{type:[Number,Object],default:()=>V},layout:String,colon:{type:Boolean,default:void 0},labelStyle:{type:Object,default:void 0},contentStyle:{type:Object,default:void 0}}),Z=Symbol("descriptionsContext"),C=Q({compatConfig:{MODE:3},name:"ADescriptions",inheritAttrs:!1,props:ve(),slots:Object,Item:ge,setup(e,t){let{slots:l,attrs:o}=t;const{prefixCls:n,direction:a}=oe("descriptions",e);let c;const r=O({}),[s,u]=be(n),p=de();ie(()=>{c=p.value.subscribe(m=>{typeof e.column=="object"&&(r.value=m)})}),ae(()=>{p.value.unsubscribe(c)}),se(Z,{labelStyle:F(e,"labelStyle"),contentStyle:F(e,"contentStyle")});const L=re(()=>Se(e.column,r.value));return()=>{var m,x,b;const{size:y,bordered:w=!1,layout:M="horizontal",colon:P=!0,title:g=(m=l.title)===null||m===void 0?void 0:m.call(l),extra:S=(x=l.extra)===null||x===void 0?void 0:x.call(l)}=e,f=(b=l.default)===null||b===void 0?void 0:b.call(l),h=$e(f,L.value);return s(i("div",G(G({},o),{},{class:[n.value,{[`${n.value}-${y}`]:y!=="default",[`${n.value}-bordered`]:!!w,[`${n.value}-rtl`]:a.value==="rtl"},o.class,u.value]}),[(g||S)&&i("div",{class:`${n.value}-header`},[g&&i("div",{class:`${n.value}-title`},[g]),S&&i("div",{class:`${n.value}-extra`},[S])]),i("div",{class:`${n.value}-view`},[i("table",null,[i("tbody",null,[h.map(($,v)=>i(ue,{key:v,index:v,colon:P,prefixCls:n.value,vertical:M==="vertical",bordered:w,row:$},null))])])])]))}}});C.install=function(e){return e.component(C.name,C),e.component(C.Item.name,C.Item),e};export{C as D,ge as a};
