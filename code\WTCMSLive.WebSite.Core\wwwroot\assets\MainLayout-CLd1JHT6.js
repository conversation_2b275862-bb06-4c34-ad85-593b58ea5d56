import{u as D}from"./devTree-BwbHtyaz.js";import{dj as y,r as u,j as T,a as x,w as C,f as R,d as l,u as S,n as V,o as b,b as r,i as p,g as m,t as d,x as M,dk as N}from"./index-DLKlSkMo.js";import{_ as k,H as B,a as H}from"./index-D-VmndZa.js";import{_ as L}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./tools-B0tc7tWY.js";import"./initDefaultProps-DBdyDbXv.js";import"./ActionButton-DDquCBiw.js";import"./styleChecker-tOiS7TSB.js";import"./index-bX4hVoaz.js";import"./shallowequal-C1uyuyZ_.js";import"./index-BJVFa-Ub.js";import"./ChangeLanguage-rYkm4TNK.js";const j={class:"footer"},A={class:"copyright"},I={__name:"MainLayout",setup($){const a=D();y();const n=x(),c=S();u([]);const _=u([]);u([]);const f=u(window.localStorage.getItem("version")||""),h=new Date().getFullYear(),g=T(()=>n.getRoutes().filter(e=>{var t;return(t=e.meta)==null?void 0:t.parent})),v=async e=>{e=="enter"?(await a.getDevTreeDatas("template"),await n.push({name:"model",params:{id:"0"}})):(await a.getDevTreeDatas(),await n.push({path:"/"}))},w=async e=>{var i;const t=g.value.find(s=>s.path===e);if(t&&((i=t.children)!=null&&i.length)){let s=t.children.map(o=>({...o,path:`${e}/${o.path}`}));if((!a.treeDatas||!a.treeDatas.length)&&await a.getDevTreeDatas(),a.treeDatas&&a.treeDatas.length>0){const o=a.treeDatas[0];a.setDevTreeCurrentNode(o),N({name:s[0].name,id:o.key,router:n})}}else n.push(e)};return C(()=>c.path,()=>{_.value=c.matched.map(e=>({path:e.path,name:e.meta.title||e.name}))},{immediate:!0}),(e,t)=>{const i=V("router-view"),s=H,o=k;return b(),R(o,null,{default:l(()=>[r(B,{onMenuSelect:w,onChangeView:v}),r(o,{class:"centerContainer"},{default:l(()=>[r(o,null,{default:l(()=>[r(s,{style:{background:"#fff",padding:"24px",margin:0,minHeight:"280px"}},{default:l(()=>[r(i)]),_:1})]),_:1})]),_:1}),p("div",j,[p("div",A,[m(" © "+d(M(h))+" V"+d(f.value)+" ",1),t[0]||(t[0]=p("span",null,"配置网站",-1)),t[1]||(t[1]=m(" All Rights Reserved ",-1))])])]),_:1})}}},W=L(I,[["__scopeId","data-v-f737b0ee"]]);export{W as default};
