import{u as U,a as X,r as h,w as B,c as L,o as S,b as T,d as w,F as te,e as ae,f as F,g as H,t as K,l as ne,h as Z,i as b,j as J,k as se,m as oe,n as re,p as le,v as ie,q as Q,s as z,x as ce}from"./index-DLKlSkMo.js";import{_ as j}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{_ as ue,M as de}from"./ActionButton-DDquCBiw.js";import{u as ee}from"./devTree-BwbHtyaz.js";import{_ as pe}from"./index-W_N8ii3N.js";import{_ as me}from"./index-DzeUeE5s.js";import{_ as he,H as ve,L as _e,a as fe}from"./index-D-VmndZa.js";import{S as ge}from"./tools-B0tc7tWY.js";import"./styleChecker-tOiS7TSB.js";import"./index-bX4hVoaz.js";import"./initDefaultProps-DBdyDbXv.js";import"./shallowequal-C1uyuyZ_.js";import"./index-C5qlGDhC.js";import"./index-BJVFa-Ub.js";import"./ChangeLanguage-rYkm4TNK.js";const ye={class:"side-navigation"},Ne={__name:"SideNavigation",props:{currentSideNav:{type:Array,default:[]}},emits:["item-click"],setup(O,{emit:l}){const v=O,d=U();X();const a=h([]),_=h(!1),n=l,E=()=>{if(!v.currentSideNav.length)return;const i=v.currentSideNav.find(p=>p.path===d.path);if(i)return i.path;const f=v.currentSideNav.find(p=>{const V=p.path.replace(/\/:[^/]+/g,"/[^/]+");return new RegExp(`^${V}$`).test(d.path)});return(f==null?void 0:f.path)||""},$=()=>{const i=E();i&&(a.value=[i])},N=i=>{a.value=[i.path],n("item-click",i)};return B(()=>d.path,$,{immediate:!0}),B(()=>v.currentSideNav,$,{deep:!0}),(i,f)=>{const p=ue,V=de;return S(),L("div",ye,[T(V,{selectedKeys:a.value,"onUpdate:selectedKeys":f[0]||(f[0]=g=>a.value=g),mode:"horizontal",collapsed:_.value},{default:w(()=>[(S(!0),L(te,null,ae(O.currentSideNav,g=>(S(),F(p,{key:g.path,title:i.$t(g.meta.title),onClick:P=>N(g)},{default:w(()=>[H(K(i.$t(g.meta.title)),1)]),_:2},1032,["title","onClick"]))),128))]),_:1},8,["selectedKeys","collapsed"])])}}},Te=j(Ne,[["__scopeId","data-v-e4e3b23e"]]),De={class:"treeContainer"},Ce={class:"treeContent"},ke={key:0},xe={style:{color:"#f50"}},Se={key:1},we={__name:"index",props:{treeDatas:{type:Array,default:[]}},emits:["node-click"],setup(O,{emit:l}){const v=U(),d=ee(),a=h([]),_=h(),n=h(""),E=h(!0),$=l,N=h(),i=O,f=()=>{if(i.treeDatas&&i.treeDatas.length&&(a.value=i.treeDatas,v.params&&v.params.id)){const m=d.getNodeById(v.params.id);let s=d.findAncestorsWithNodes(v.params.id);s&&s.length&&(_.value=s.map(D=>D.key)),m&&(N.value=[m.key])}};B(()=>i.treeDatas,m=>{a.value=m,f()},{deep:!0}),B(()=>v.path,()=>{f()},{immediate:!0});const p=(m,s,D)=>{for(let M=0;M<D.length;M++){const c=D[M];c.title.indexOf(m)>-1?s.push(c.key):c.children&&c.children.length&&(s=p(m,s,c.children))}return s},V=(m,s)=>{$("node-click",s),N.value=[s.node.key]},g=m=>{_.value=m},P=ne.debounce(m=>{const s=[];p(m,s,a.value),_.value=s,n.value=m,m||(_.value=[])},300);return B(n,P),Z(()=>{f()}),(m,s)=>{const D=pe,M=me;return S(),L("div",De,[T(D,{value:n.value,"onUpdate:value":s[0]||(s[0]=c=>n.value=c),style:{"margin-top":"8px"},placeholder:"搜索",class:"search"},null,8,["value"]),b("div",Ce,[T(M,{"expanded-keys":_.value,selectedKeys:N.value,"onUpdate:selectedKeys":s[1]||(s[1]=c=>N.value=c),"auto-expand-parent":E.value,"tree-data":a.value,onExpand:g,onSelect:V},{title:w(({title:c})=>[c.indexOf(n.value)>-1?(S(),L("span",ke,[H(K(c.substring(0,c.indexOf(n.value)))+" ",1),b("span",xe,K(n.value),1),H(" "+K(c.substring(c.indexOf(n.value)+n.value.length)),1)])):(S(),L("span",Se,K(c),1))]),_:1},8,["expanded-keys","selectedKeys","auto-expand-parent","tree-data"])])])}}},$e=j(we,[["__scopeId","data-v-4662ac14"]]),Re={class:"devTreeContainer"},Ie={class:"templateText"},Ve={key:0,class:"rightContentTop"},Me={key:1,class:"footer"},be={class:"copyright"},Ke={__name:"MainLayoutOfTree",setup(O){const l=ee(),v=h(!1),d=X(),a=U(),_=h(!1),n=h([]),E=h([]),$=h([]),N=h(null),i=h(window.localStorage.getItem("version")||""),f=new Date().getFullYear(),p=h(window.localStorage.getItem("templateManagement")==="true"),V=J(()=>d.getRoutes().filter(e=>{var t;return(t=e.meta)==null?void 0:t.parent})),g=J(()=>{var e;return!(n.value.length&&n.value.length==1&&((e=n.value[0].meta)!=null&&e.hideMenu))}),P=(e,t)=>{var o,x,R,A;e||(e="/monitor");let u=t?t.node.key:(o=a.params)==null?void 0:o.id,C=t?t.node.type:(x=a.meta)==null?void 0:x.selectNodeType;const k=t?t.node:l.getNodeById(u);if(k.type!==C){let y=k.value||l.getDevTreeCurrentNode();s(y)}const r=V.value.find(y=>y.path===e);if(r&&((R=r.children)!=null&&R.length)){let y=[],q=r.path;(A=r.meta)!=null&&A.isDiffrentChildren?r.children.map(I=>{var W,Y,G;!((W=I.meta)!=null&&W.templateViewHidden&&p.value)&&((Y=I.meta)!=null&&Y.selectNodeType)&&((G=I.meta)==null?void 0:G.selectNodeType)==C&&y.push({...I,path:`${q}/${I.path}`})}):r.children.map(I=>{y.push({...I,path:`${q}/${I.path}`})}),n.value=y,y.length>0&&u&&d.push({name:y[0].name,params:{id:u}})}else d.push(e),n.value=[]},m=e=>{var u;let t=(u=a.params)==null?void 0:u.id;t&&d.push({name:e.name,params:{id:t}})},s=e=>{var C,k;N.value=e.node;let t=(C=a.meta)==null?void 0:C.selectNodeType;const u=a;if((k=a.meta)!=null&&k.isDiffrentChildren&&t&&t!==e.node.type&&a.matched.length>1&&a.matched[0].children.length>0){let r=a.matched[0].path,o=[];if(a.matched[0].children.map(x=>{var R;!((R=x.meta)!=null&&R.templateViewHidden&&p.value)&&x.meta.selectNodeType==e.node.type&&o.push({...x,path:`${r}/${x.path}`})}),o&&o.length>0)n.value=o;else{P(!1,e);return}}l.setDevTreeCurrentNode(e.node),t&&t===e.node.type?d.push({name:u.name,params:{id:e.node.key}}):n.value.length>0&&d.push({name:n.value[0].name,params:{id:e.node.key}})},D=()=>{var C,k;const t=a.matched;let u=null;for(let r=t.length-1;r>=0;r--){const o=t[r];if((C=o.meta)!=null&&C.parent){u=o;break}}if(u){let r=[];(k=u.meta)!=null&&k.isDiffrentChildren&&t.length>1?u.children.map(o=>{var x,R,A,y;!((x=o.meta)!=null&&x.templateViewHidden&&p.value)&&((R=o.meta)!=null&&R.selectNodeType)&&((A=o.meta)==null?void 0:A.selectNodeType)==((y=t[1].meta)==null?void 0:y.selectNodeType)&&r.push({...o,path:`${u.path}/${o.path}`})}):u.children.map(o=>{r.push({...o,path:`${u.path}/${o.path}`})}),n.value=r}else n.value=[]};se(async()=>{try{v.value=!0,await l.getDevTreeDatas(p.value?"template":""),l.treeDatas.length||oe.error("获取设备树数据失败"),c(),_.value=!0,E.value=a.matched.map(e=>({path:e.path,name:e.meta.title||e.name}))}catch(e){console.error("获取设备树数据失败:",e),_.value=!0}finally{v.value=!1}}),Z(()=>{d.isReady?d.isReady().then(()=>{D()}):D()}),B(()=>a.path,()=>{E.value=a.matched.map(e=>({path:e.path,name:e.meta.title||e.name}))},{immediate:!0}),B(()=>l.treeDatas,()=>{$.value=[...l.treeDatas],N.value=l.getDevTreeCurrentNode()});const M=async e=>{e=="enter"?(p.value=!0,await l.getDevTreeDatas("template"),await d.push({name:"model",params:{id:"0"}})):(p.value=!1,await l.getDevTreeDatas(),await d.push({path:"/"})),D()},c=()=>{l.treeDatas&&l.treeDatas.length&&($.value=[...l.treeDatas],N.value=l.getDevTreeCurrentNode())};return(e,t)=>{const u=_e,C=re("router-view"),k=fe,r=he,o=ge;return S(),F(r,null,{default:w(()=>[T(ve,{onMenuSelect:P,onChangeView:M,currentTreeNode:N.value},null,8,["currentTreeNode"]),T(o,{spinning:v.value,size:"large"},{default:w(()=>[T(r,{class:"centerContainer"},{default:w(()=>[T(u,{width:"260"},{default:w(()=>[b("div",Re,[le(b("p",Ie,"模版管理中心",512),[[ie,p.value]]),T($e,{treeDatas:$.value,onNodeClick:s},null,8,["treeDatas"])])]),_:1}),T(r,{class:Q(["rightContent",p.value?"templateBorder":""])},{default:w(()=>[g.value?(S(),L("div",Ve,[T(Te,{currentSideNav:n.value,onItemClick:m},null,8,["currentSideNav"])])):z("",!0),b("div",{class:Q(g.value?"rightContentContent":null)},[_.value?(S(),F(k,{key:0,class:"content"},{default:w(()=>[T(C)]),_:1})):z("",!0)],2),_.value?(S(),L("div",Me,[b("div",be,[H(" © "+K(ce(f))+" V"+K(i.value)+" ",1),t[0]||(t[0]=b("span",null,"配置网站",-1)),t[1]||(t[1]=H(" All Rights Reserved ",-1))])])):z("",!0)]),_:1},8,["class"])]),_:1})]),_:1},8,["spinning"])]),_:1})}}},Je=j(Ke,[["__scopeId","data-v-b7189073"]]);export{Je as default};
