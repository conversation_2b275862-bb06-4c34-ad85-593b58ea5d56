using Microsoft.AspNetCore.SignalR;
using RealtimePerf.Hubs;
using System.Collections.Concurrent;
using System.Globalization;
using System.Text;
using WTCMSLive.WebSite.Core.Models.DTOs;

namespace WTCMSLive.WebSite.Core.Service
{
    /// <summary>
    /// 服务日志管理服务
    /// </summary>
    public class ServiceLogService : IServiceLogService
    {
        private readonly ILogger<ServiceLogService> _logger;
        private readonly IConfiguration _configuration;
        private readonly IServiceManagementService _serviceManagementService;
        private readonly IHubContext<ServerPerformanceHub> _hubContext;
        
        // 实时日志监控器
        private readonly ConcurrentDictionary<string, FileSystemWatcher> _logWatchers;
        private readonly ConcurrentDictionary<string, HashSet<string>> _activeConnections;
        private readonly ConcurrentDictionary<string, long> _lastFilePositions;

        public ServiceLogService(
            ILogger<ServiceLogService> logger,
            IConfiguration configuration,
            IServiceManagementService serviceManagementService,
            IHubContext<ServerPerformanceHub> hubContext)
        {
            _logger = logger;
            _configuration = configuration;
            _serviceManagementService = serviceManagementService;
            _hubContext = hubContext;
            _logWatchers = new ConcurrentDictionary<string, FileSystemWatcher>();
            _activeConnections = new ConcurrentDictionary<string, HashSet<string>>();
            _lastFilePositions = new ConcurrentDictionary<string, long>();
        }

        /// <summary>
        /// 获取服务根目录
        /// </summary>
        public async Task<string> GetServiceRootDirectoryAsync(string serviceId)
        {
            try
            {
                var config = await _serviceManagementService.GetServiceConfigAsync(serviceId);
                if (config == null)
                {
                    throw new ArgumentException($"服务配置不存在: {serviceId}");
                }

                // 使用ServiceConfigs中配置的日志路径
                if (!string.IsNullOrEmpty(config.LogRootPath))
                {
                    return config.LogRootPath;
                }

                // 如果ServiceConfigs中没有配置日志路径，则抛出异常
                throw new InvalidOperationException($"服务 {serviceId} 未配置日志根目录路径，请在ServiceConfigs.json中添加logRootPath字段");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取服务根目录失败: {ServiceId}", serviceId);
                throw;
            }
        }

        /// <summary>
        /// 检查日志目录是否存在
        /// </summary>
        public async Task<bool> CheckLogDirectoryExistsAsync(string serviceId, string logType)
        {
            try
            {
                var serviceRoot = await GetServiceRootDirectoryAsync(serviceId);
                var logDirectory = Path.Combine(serviceRoot, GetLogDirectoryName(logType));
                return Directory.Exists(logDirectory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查日志目录失败: {ServiceId}, {LogType}", serviceId, logType);
                return false;
            }
        }

        /// <summary>
        /// 获取服务日志文件列表
        /// </summary>
        public async Task<List<LogFileInfoDTO>> GetLogFilesAsync(string serviceId, string logType, DateTime? startTime = null, DateTime? endTime = null)
        {
            try
            {
                var serviceRoot = await GetServiceRootDirectoryAsync(serviceId);
                var logDirectory = Path.Combine(serviceRoot, GetLogDirectoryName(logType));

                if (!Directory.Exists(logDirectory))
                {
                    _logger.LogWarning("日志目录不存在: {LogDirectory}", logDirectory);
                    return new List<LogFileInfoDTO>();
                }

                var logFiles = new List<LogFileInfoDTO>();

                // 遍历年月目录
                var yearMonthDirs = Directory.GetDirectories(logDirectory)
                    .Where(dir => IsValidYearMonthDirectory(Path.GetFileName(dir)))
                    .OrderByDescending(dir => Path.GetFileName(dir));

                foreach (var yearMonthDir in yearMonthDirs)
                {
                    var yearMonth = Path.GetFileName(yearMonthDir);
                    
                    // 如果指定了时间范围，检查是否在范围内
                    if (startTime.HasValue || endTime.HasValue)
                    {
                        if (!IsYearMonthInRange(yearMonth, startTime, endTime))
                            continue;
                    }

                    // 获取该月份下的所有日志文件
                    var files = Directory.GetFiles(yearMonthDir, "*.log")
                        .OrderByDescending(f => Path.GetFileName(f));

                    foreach (var file in files)
                    {
                        var fileInfo = new FileInfo(file);
                        var fileName = Path.GetFileName(file);

                        // 如果指定了时间范围，检查文件日期是否在范围内
                        if (startTime.HasValue || endTime.HasValue)
                        {
                            var fileDate = GetFileDateFromName(fileName);
                            if (fileDate.HasValue)
                            {
                                if (startTime.HasValue && fileDate.Value.Date < startTime.Value.Date)
                                    continue;
                                if (endTime.HasValue && fileDate.Value.Date > endTime.Value.Date)
                                    continue;
                            }
                        }

                        logFiles.Add(new LogFileInfoDTO
                        {
                            FileName = fileName,
                            FilePath = file,
                            FileSize = fileInfo.Length,
                            FileSizeFormatted = FormatFileSize(fileInfo.Length),
                            CreatedTime = fileInfo.CreationTime,
                            ModifiedTime = fileInfo.LastWriteTime,
                            LogType = logType
                        });
                    }
                }

                return logFiles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取日志文件列表失败: {ServiceId}, {LogType}", serviceId, logType);
                throw;
            }
        }

        /// <summary>
        /// 获取日志文件内容
        /// </summary>
        public async Task<LogContentDTO> GetLogContentAsync(string serviceId, string logType, string fileName)
        {
            try
            {
                var serviceRoot = await GetServiceRootDirectoryAsync(serviceId);
                var logDirectory = Path.Combine(serviceRoot, GetLogDirectoryName(logType));

                // 查找文件（可能在不同的年月目录下）
                var filePath = FindLogFile(logDirectory, fileName);
                if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                {
                    throw new FileNotFoundException($"日志文件不存在: {fileName}");
                }

                var fileInfo = new FileInfo(filePath);
                // 日志文件的编码为 UTF-8
                var content = await File.ReadAllTextAsync(filePath, Encoding.GetEncoding("UTF-8"));

                return new LogContentDTO
                {
                    FileName = fileName,
                    Content = content,
                    FileSize = fileInfo.Length,
                    LastModified = fileInfo.LastWriteTime,
                    LogType = logType
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取日志文件内容失败: {ServiceId}, {LogType}, {FileName}", serviceId, logType, fileName);
                throw;
            }
        }

        /// <summary>
        /// 开始实时日志监控
        /// </summary>
        public async Task<bool> StartRealTimeLogAsync(string serviceId, string logType, string connectionId)
        {
            try
            {
                var watcherKey = $"{serviceId}_{logType}";
                
                // 添加连接到活跃连接列表
                _activeConnections.AddOrUpdate(watcherKey, 
                    new HashSet<string> { connectionId },
                    (key, existing) => { existing.Add(connectionId); return existing; });

                // 如果监控器已存在，直接返回成功
                if (_logWatchers.ContainsKey(watcherKey))
                {
                    _logger.LogInformation("实时日志监控已存在，添加连接: {ServiceId}, {LogType}, {ConnectionId}", serviceId, logType, connectionId);
                    return true;
                }

                var serviceRoot = await GetServiceRootDirectoryAsync(serviceId);
                var logDirectory = Path.Combine(serviceRoot, GetLogDirectoryName(logType));

                if (!Directory.Exists(logDirectory))
                {
                    _logger.LogWarning("日志目录不存在，无法启动实时监控: {LogDirectory}", logDirectory);
                    return false;
                }

                // 创建文件监控器
                var watcher = new FileSystemWatcher(logDirectory)
                {
                    Filter = "*.log",
                    IncludeSubdirectories = true,
                    NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.Size
                };

                watcher.Changed += async (sender, e) => await OnLogFileChanged(serviceId, logType, e.FullPath);
                watcher.EnableRaisingEvents = true;

                _logWatchers.TryAdd(watcherKey, watcher);

                // 发送当前最新日志的末尾内容
                await SendLatestLogContent(serviceId, logType, connectionId);

                _logger.LogInformation("开始实时日志监控: {ServiceId}, {LogType}, {ConnectionId}", serviceId, logType, connectionId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "开始实时日志监控失败: {ServiceId}, {LogType}, {ConnectionId}", serviceId, logType, connectionId);
                return false;
            }
        }

        /// <summary>
        /// 停止实时日志监控
        /// </summary>
        public async Task<bool> StopRealTimeLogAsync(string serviceId, string logType, string connectionId)
        {
            try
            {
                var watcherKey = $"{serviceId}_{logType}";

                // 从活跃连接列表中移除连接
                if (_activeConnections.TryGetValue(watcherKey, out var connections))
                {
                    connections.Remove(connectionId);
                    
                    // 如果没有活跃连接了，停止监控器
                    if (connections.Count == 0)
                    {
                        _activeConnections.TryRemove(watcherKey, out _);
                        
                        if (_logWatchers.TryRemove(watcherKey, out var watcher))
                        {
                            watcher.EnableRaisingEvents = false;
                            watcher.Dispose();
                            _logger.LogInformation("停止实时日志监控器: {ServiceId}, {LogType}", serviceId, logType);
                        }

                        _lastFilePositions.TryRemove(watcherKey, out _);
                    }
                }

                _logger.LogInformation("停止实时日志监控: {ServiceId}, {LogType}, {ConnectionId}", serviceId, logType, connectionId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止实时日志监控失败: {ServiceId}, {LogType}, {ConnectionId}", serviceId, logType, connectionId);
                return false;
            }
        }

        #region 私有方法

        /// <summary>
        /// 获取日志目录名称
        /// </summary>
        private string GetLogDirectoryName(string logType)
        {
            return logType.ToLowerInvariant() switch
            {
                "debuggerlog" => "DebugLog",
                "errorlog" => "ErrorLog",
                _ => throw new ArgumentException($"不支持的日志类型: {logType}")
            };
        }

        /// <summary>
        /// 检查是否为有效的年月目录
        /// </summary>
        private bool IsValidYearMonthDirectory(string dirName)
        {
            return dirName.Length == 6 && 
                   int.TryParse(dirName.Substring(0, 4), out var year) && year >= 2000 && year <= 9999 &&
                   int.TryParse(dirName.Substring(4, 2), out var month) && month >= 1 && month <= 12;
        }

        /// <summary>
        /// 检查年月是否在指定范围内
        /// </summary>
        private bool IsYearMonthInRange(string yearMonth, DateTime? startTime, DateTime? endTime)
        {
            if (!DateTime.TryParseExact(yearMonth + "01", "yyyyMMdd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var monthDate))
                return false;

            if (startTime.HasValue && monthDate.AddMonths(1).AddDays(-1) < startTime.Value.Date)
                return false;

            if (endTime.HasValue && monthDate > endTime.Value.Date)
                return false;

            return true;
        }

        /// <summary>
        /// 从文件名获取日期
        /// </summary>
        private DateTime? GetFileDateFromName(string fileName)
        {
            try
            {
                // 文件名格式: 20220824.0.log
                var parts = fileName.Split('.');
                if (parts.Length >= 2 && parts[0].Length == 8)
                {
                    if (DateTime.TryParseExact(parts[0], "yyyyMMdd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var date))
                    {
                        return date;
                    }
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// 查找日志文件
        /// </summary>
        private string FindLogFile(string logDirectory, string fileName)
        {
            try
            {
                // 首先在根目录查找
                var directPath = Path.Combine(logDirectory, fileName);
                if (File.Exists(directPath))
                    return directPath;

                // 在年月子目录中查找
                var yearMonthDirs = Directory.GetDirectories(logDirectory)
                    .Where(dir => IsValidYearMonthDirectory(Path.GetFileName(dir)));

                foreach (var yearMonthDir in yearMonthDirs)
                {
                    var filePath = Path.Combine(yearMonthDir, fileName);
                    if (File.Exists(filePath))
                        return filePath;
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查找日志文件失败: {LogDirectory}, {FileName}", logDirectory, fileName);
                return null;
            }
        }

        /// <summary>
        /// 日志文件变化事件处理
        /// </summary>
        private async Task OnLogFileChanged(string serviceId, string logType, string filePath)
        {
            try
            {
                var watcherKey = $"{serviceId}_{logType}";
                
                if (!_activeConnections.TryGetValue(watcherKey, out var connections) || connections.Count == 0)
                    return;

                // 获取文件的新内容
                var newContent = await ReadNewLogContent(filePath, watcherKey);
                if (string.IsNullOrEmpty(newContent))
                    return;

                // 解析日志内容并发送
                var logLines = newContent.Split('\n', StringSplitOptions.RemoveEmptyEntries);
                foreach (var line in logLines)
                {
                    if (string.IsNullOrWhiteSpace(line))
                        continue;

                    var logItem = new RealTimeLogItemDTO
                    {
                        ServiceId = serviceId,
                        LogType = logType,
                        Timestamp = DateTime.Now,
                        Content = line.Trim(),
                        Level = ExtractLogLevel(line)
                    };

                    // 发送给所有活跃连接
                    foreach (var connectionId in connections.ToList())
                    {
                        try
                        {
                            await _hubContext.Clients.Client(connectionId).SendAsync("ReceiveServiceLog", logItem);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "发送实时日志失败，移除连接: {ConnectionId}", connectionId);
                            connections.Remove(connectionId);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理日志文件变化失败: {ServiceId}, {LogType}, {FilePath}", serviceId, logType, filePath);
            }
        }

        /// <summary>
        /// 读取新的日志内容
        /// </summary>
        private async Task<string> ReadNewLogContent(string filePath, string watcherKey)
        {
            try
            {
                var fileInfo = new FileInfo(filePath);
                if (!fileInfo.Exists)
                    return null;

                var currentSize = fileInfo.Length;
                var lastPosition = _lastFilePositions.GetOrAdd(watcherKey, 0);

                if (currentSize <= lastPosition)
                {
                    // 文件可能被重新创建，从头开始读取
                    lastPosition = 0;
                }

                using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                fileStream.Seek(lastPosition, SeekOrigin.Begin);

                using var reader = new StreamReader(fileStream, Encoding.GetEncoding("UTF-8"));
                var newContent = await reader.ReadToEndAsync();

                _lastFilePositions.TryUpdate(watcherKey, currentSize, lastPosition);

                return newContent;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "读取新日志内容失败: {FilePath}", filePath);
                return null;
            }
        }

        /// <summary>
        /// 发送最新日志内容
        /// </summary>
        private async Task SendLatestLogContent(string serviceId, string logType, string connectionId)
        {
            try
            {
                var serviceRoot = await GetServiceRootDirectoryAsync(serviceId);
                var logDirectory = Path.Combine(serviceRoot, GetLogDirectoryName(logType));

                // 找到最新的日志文件
                var latestFile = GetLatestLogFile(logDirectory);
                if (string.IsNullOrEmpty(latestFile))
                    return;

                // 读取文件末尾的内容（最后100行）
                var lines = await ReadLastLines(latestFile, 100);
                foreach (var line in lines)
                {
                    if (string.IsNullOrWhiteSpace(line))
                        continue;

                    var logItem = new RealTimeLogItemDTO
                    {
                        ServiceId = serviceId,
                        LogType = logType,
                        Timestamp = DateTime.Now,
                        Content = line.Trim(),
                        Level = ExtractLogLevel(line)
                    };

                    await _hubContext.Clients.Client(connectionId).SendAsync("ReceiveServiceLog", logItem);
                }

                // 记录当前文件位置
                var watcherKey = $"{serviceId}_{logType}";
                var fileInfo = new FileInfo(latestFile);
                _lastFilePositions.TryAdd(watcherKey, fileInfo.Length);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送最新日志内容失败: {ServiceId}, {LogType}, {ConnectionId}", serviceId, logType, connectionId);
            }
        }

        /// <summary>
        /// 获取最新的日志文件
        /// </summary>
        private string GetLatestLogFile(string logDirectory)
        {
            try
            {
                if (!Directory.Exists(logDirectory))
                    return null;

                // 获取最新的年月目录
                var latestYearMonthDir = Directory.GetDirectories(logDirectory)
                    .Where(dir => IsValidYearMonthDirectory(Path.GetFileName(dir)))
                    .OrderByDescending(dir => Path.GetFileName(dir))
                    .FirstOrDefault();

                if (string.IsNullOrEmpty(latestYearMonthDir))
                    return null;

                // 获取该目录下最新的日志文件
                var latestFile = Directory.GetFiles(latestYearMonthDir, "*.log")
                    .OrderByDescending(f => Path.GetFileName(f))
                    .FirstOrDefault();

                return latestFile;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取最新日志文件失败: {LogDirectory}", logDirectory);
                return null;
            }
        }

        /// <summary>
        /// 读取文件的最后几行
        /// </summary>
        private async Task<List<string>> ReadLastLines(string filePath, int lineCount)
        {
            try
            {
                var lines = new List<string>();
                using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                using var reader = new StreamReader(fileStream, Encoding.GetEncoding("UTF-8"));

                var allLines = new List<string>();
                string line;
                while ((line = await reader.ReadLineAsync()) != null)
                {
                    allLines.Add(line);
                }

                // 返回最后的指定行数
                var startIndex = Math.Max(0, allLines.Count - lineCount);
                return allLines.Skip(startIndex).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "读取文件最后几行失败: {FilePath}", filePath);
                return new List<string>();
            }
        }

        /// <summary>
        /// 提取日志级别
        /// </summary>
        private string ExtractLogLevel(string logLine)
        {
            try
            {
                var upperLine = logLine.ToUpperInvariant();
                if (upperLine.Contains("ERROR") || upperLine.Contains("ERR"))
                    return "ERROR";
                if (upperLine.Contains("WARN") || upperLine.Contains("WARNING"))
                    return "WARN";
                if (upperLine.Contains("INFO"))
                    return "INFO";
                if (upperLine.Contains("DEBUG"))
                    return "DEBUG";
                return "INFO";
            }
            catch
            {
                return "INFO";
            }
        }

        #endregion
    }
}
