import{u as ae,W}from"./table-C0LB8EJD.js";import{O as se}from"./index-ChYhAQZ_.js";/* empty css                                                              */import{S as oe,d as A}from"./tools-B0tc7tWY.js";import{r as p,j as le,u as ie,y as ne,w as ue,f as B,d as E,o as w,c as G,b as M,m as u,aR as U}from"./index-DLKlSkMo.js";import{u as re}from"./configModbus-B_zlnUGH.js";import{u as de}from"./collectionUnitConfig-BYxKHJs0.js";import{u as ce}from"./devTree-BwbHtyaz.js";import{M as be}from"./index-BJVFa-Ub.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./ActionButton-DDquCBiw.js";import"./styleChecker-tOiS7TSB.js";import"./index-bX4hVoaz.js";import"./initDefaultProps-DBdyDbXv.js";import"./shallowequal-C1uyuyZ_.js";import"./index-W_N8ii3N.js";import"./index-DzeUeE5s.js";import"./index-C5qlGDhC.js";import"./index-pJrpCCGr.js";import"./index-Big_Av0Y.js";const pe={key:1},qe={__name:"modbus",setup(me){const n=re(),x=de(),$=ce(),j=ae();let C=[{label:"串口服务器",value:"0",text:"串口服务器"},{label:"串口直连",value:"1",text:"串口直连"}];const L=(e={isForm:!1})=>[{title:"ModbusID",dataIndex:"modbusID",columnWidth:100,validateRules:A({type:"number",title:"设备ID",required:!0})},{title:"Modbus名称",dataIndex:"modbusDeviceName",isrequired:!0},{title:"Modbus类型",dataIndex:"modbusType",columnWidth:150,isrequired:!0,inputType:"select",selectOptions:[],...e&&e.isForm?{}:{customRender:({text:a,record:t})=>{const o=n.modbusDevTypes.find(l=>l.value==t.modbusType);return o?o.label:a}}},{title:"采集单元",dataIndex:"dauID",isrequired:!0,inputType:"select",selectOptions:[],...e&&e.isForm?{}:{customRender:({text:a,record:t})=>{const o=x.dAUOptionList.find(l=>l.value==t.dauID);return o?o.label:a}}},{title:"连接方式",dataIndex:"comType",isrequired:!0,inputType:"radio",hasChangeEvent:!0,selectOptions:C,headerOperations:{filters:C},...e&&e.isForm?{}:{customRender:({text:a,record:t})=>{const o=C.find(l=>l.value==t.comType);return o?o.label:a}}}],O=(e={isForm:!1})=>[{title:"设备名称",dataIndex:"modbusDeviceID",isrequired:!0,inputType:"select",selectOptions:[],hasChangeEvent:!0,...e&&e.isForm?{}:{customRender:({text:a,record:t})=>t.modbusDeviceName||""}},{title:"通道编号",dataIndex:"channelNumber",validateRules:A({type:"number",title:"通道编号",required:!0})},{title:"测量位置",dataIndex:"measLocationID",isrequired:!0,inputType:"select",selectOptions:[],...e&&e.isForm?{}:{customRender:({text:a,record:t})=>t.measLocationName}}],d=()=>[{title:"串口服务器IP",dataIndex:"comIP",validateRules:A({type:"ip",title:"串口服务器IP",required:!0})},{title:"串口服务器端口",dataIndex:"comPort",isrequired:!0,columnWidth:100,validateRules:A({type:"port",title:"串口服务器端口"})},{title:"串口名称",dataIndex:"portName",isrequired:!0},{title:"波特率",dataIndex:"baudRate",columnWidth:100,validateRules:A({type:"number",title:"波特率",required:!0})},{title:"数据位",dataIndex:"dataBit",isrequired:!0,columnWidth:100},{title:"校验位",dataIndex:"parity",isrequired:!0,columnWidth:100,inputType:"radio",selectOptions:[{label:"无",value:0},{label:"奇",value:1},{label:"偶",value:2}]},{title:"停止位",dataIndex:"stopBit",isrequired:!0,columnWidth:100}],I=p(!1),v=p(""),m=p(""),z=le(()=>m.value==="batchAdd"?"1200px":"600px"),c=p(""),k=p(!1),_=ie(),f=p({}),r=p([]),i=p(_.params.id),R=p(),s=ne({tableColumns:[...L(),{title:"串口服务器IP",dataIndex:"comIP"},{title:"串口服务器端口",dataIndex:"comPort"},{title:"串口名称",dataIndex:"portName"},{title:"波特率",dataIndex:"baudRate"}],tableColumns2:O(),tableData1:[],tableData2:[],batchApplyData:[],batchApplyKey:"",bathApplyResponse1:{},bathApplyResponse2:{}}),V=async e=>{R.value&&await j.fetchDevTreedDevicelist({windParkID:R.value,useTobath:!0})},H=()=>{let e=$.findAncestorsWithNodes(i.value);e&&e.length&&e.length>1&&(R.value=e[e.length-2].id)},J=async()=>{await n.fetchGetModbusDevType()},Q=async()=>{await x.fetchGetDAUList({WindTurbineID:i.value,WindParkId:R.value})},X=async e=>{i.value&&await n.fetchGetModbusMeasLocByDeviceID({turbineID:i.value,modbusDeviceID:e})},T=async()=>{I.value=!0;let e=await n.fetchGetModbusDeviceList({turbineID:i.value});e&&(I.value=!1,s.tableData1=e)},g=async()=>{I.value=!0;let e=await n.fetchGetModbusChannelList({turbineID:i.value});e&&(I.value=!1,s.tableData2=e)};ue(()=>_.params.id,async e=>{i.value=e,e&&(H(),await V(),J(),Q(),T(),g())},{immediate:!0});const F=e=>{const{title:a,operateType:t,tableKey:o}=e;switch(m.value=t,c.value=o,o){case"1":v.value="增加Modbus设备";break;case"2":v.value="增加Modbus通道";break}S(),P()},S=()=>{switch(c.value){case"1":let e=[...L({isForm:!0}),d()[0],d()[1]];e[2].selectOptions=n.modbusDevTypes,e[3].selectOptions=[{label:"无",value:-1},...x.dAUOptionList],m.value==="edit"&&(e[3].disabled=!0),f.value={comType:"0",dataBit:8,parity:0,stopBit:0},r.value=e;break;case"2":let a=[...O({isForm:!0})];a[0].selectOptions=n.modbusDeviceOptions,r.value=a;break}},q=e=>{const{tableKey:a,rowData:t,operateType:o}=e;switch(m.value=o,c.value=a,S(),a){case"1":v.value="编辑Modbus设备",K({value:t.comType,dataIndex:"comType"});break;case"2":v.value="编辑Modbus通道";break}f.value={...t,dauID:t.dauID&&t.dauID!==""?t.dauID:-1},P()},Y=async e=>{switch(m.value){case"add":await Z(e);break;case"edit":await ee(e);break}},Z=async e=>{if(c.value==="1"){let a={...e,turbineID:i.value,dauID:e.dauID&&e.dauID!==-1?e.dauID:""};const t=await n.fetchAddModbusDevice({sourceData:a,targetTurbineIds:s.batchApplyData});t&&t.code==1?(T(),s.bathApplyResponse1=t.batchResults||{},D(),u.success("提交成功")):u.error("提交失败:"+t.msg)}else if(c.value==="2"){let a=[{windTurbineID:i.value,channelNumber:e.channelNumber,measLocationID:e.measLocationID,modbusDeviceID:e.modbusDeviceID,description:""}];const t=await n.fetchAddModbusChannel({sourceData:a,targetTurbineIds:s.batchApplyData});t&&t.code==1?(u.success("提交成功"),g(),s.bathApplyResponse2=t.batchResults||{},D()):u.error("提交失败:"+t.msg)}},ee=async e=>{if(c.value==="1"){let a={...f.value,...e,modbusID:e.modbusID,dauID:e.dauID&&e.dauID!==-1?e.dauID:""};const t=await n.fetchEditModbusDevice({sourceData:a,targetTurbineIds:s.batchApplyData});t&&t.code===1?(T(),s.bathApplyResponse1=t.batchResults||{},D(),u.success("提交成功")):u.error("提交失败:"+t.msg)}else if(c.value==="2"){let a={windTurbineID:i.value,description:"",...e};const t=await n.fetchEditModbusChannel({sourceData:a,targetTurbineIds:s.batchApplyData});t&&t.code===1?(g(),s.bathApplyResponse2=t.batchResults||{},D(),u.success("提交成功")):u.error("提交失败:"+t.msg)}},N=async e=>{const{tableKey:a,selectedkeys:t,record:o}=e;if(c.value=a,a==="1"){let l=[];if(o)l.push({modbusDeviceID:o.modbusDeviceID,turbineID:i.value,modbusID:o.modbusID});else for(let h=0;h<t.length;h++){let y=t[h].split("&&");l.push({turbineID:i.value,modbusDeviceID:y[0],modbusID:y[1]})}const b=await n.fetchBatchDeleteModbusDevice({sourceData:l,targetTurbineIds:s.batchApplyData});b&&b.code==1?(T(),s.bathApplyResponse1=b.batchResults||{},D(),u.success("删除成功")):u.error("删除失败:"+b.msg)}else if(a==="2"){let l=[];if(o)l.push({windTurbineID:i.value,modbusDeviceID:o.modbusDeviceID,channelNumber:o.channelNumber,measLocationID:o.measLocationID});else for(let h=0;h<t.length;h++){let y=t[h].split("&&");l.push({windTurbineID:i.value,modbusDeviceID:y[1],channelNumber:y[0],measLocationID:y[2]})}const b=await n.fetchBatchDeleteModbusChannel({sourceData:l,targetTurbineIds:s.batchApplyData});b&&b.code==1?(g(),s.bathApplyResponse2=b.batchResults||{},u.success("删除成功"),D()):u.error("删除失败:"+b.msg)}},K=async e=>{if(e.dataIndex&&e.value&&e.dataIndex==="comType"&&(e.value=="0"?r.value=[...r.value.slice(0,5),d()[0],d()[1]]:e.value=="1"&&(r.value=[...r.value.slice(0,5),d()[2],d()[3],d()[4],d()[5],d()[6]])),e.dataIndex&&e.value&&e.dataIndex==="modbusDeviceID"){await X(e.value);let a=[...r.value];a[2].selectOptions=n.modbusMeasLocoptions,r.value=a}},P=()=>{k.value=!0},D=e=>{k.value=!1,r.value=[],f.value={},m.value="",v.value="",c.value=""},te=async e=>{e.type&&e.type=="close"?(s.batchApplyData=[],s.batchApplyKey="",s[`bathApplyResponse${e.key}`]={}):(s.batchApplyData=e.turbines,s.batchApplyKey=e.key)};return U("deviceId",i),U("bathApplySubmit",te),(e,a)=>{const t=be,o=oe;return w(),B(o,{spinning:I.value,size:"large"},{default:E(()=>[(w(),G("div",{key:i.value},[M(W,{ref:"table",size:"default","table-key":"1","table-title":"Modbus设备列表","table-columns":s.tableColumns,borderLight:s.batchApplyKey=="1",bathApplyResponse:s.bathApplyResponse1,"table-operate":["edit","delete","add","batchDelete"],recordKey:l=>`${l.modbusDeviceID}&&${l.modbusID}`,"table-datas":s.tableData1,onAddRow:F,onDeleteRow:N,onEditRow:q},null,8,["table-columns","borderLight","bathApplyResponse","recordKey","table-datas"]),M(W,{ref:"table",size:"default","table-key":"2","table-title":"通道列表","table-columns":s.tableColumns2,borderLight:s.batchApplyKey=="2",bathApplyResponse:s.bathApplyResponse2,"table-operate":["delete","add","batchDelete"],recordKey:l=>`${l.channelNumber}&&${l.modbusDeviceID}&&${l.measLocationID}`,"table-datas":s.tableData2,onAddRow:F,onDeleteRow:N,onEditRow:q},null,8,["table-columns","borderLight","bathApplyResponse","recordKey","table-datas"])])),M(t,{maskClosable:!1,width:z.value,open:k.value,title:v.value,footer:"",destroyOnClose:!0,onCancel:D},{default:E(()=>[m.value==="add"||m.value==="edit"?(w(),B(se,{key:0,titleCol:r.value,initFormData:f.value,onChange:K,onSubmit:Y},null,8,["titleCol","initFormData"])):(w(),G("div",pe))]),_:1},8,["width","open","title"])]),_:1},8,["spinning"])}}};export{qe as default};
