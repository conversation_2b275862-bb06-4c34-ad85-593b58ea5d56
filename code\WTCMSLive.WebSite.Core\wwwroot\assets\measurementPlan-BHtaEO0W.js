import{u as U,W as V}from"./table-C0LB8EJD.js";import{O as $}from"./index-ChYhAQZ_.js";import{W as H}from"./index-ZdxrKWAB.js";import{C as J,cB as Q,cC as X,cD as Y,cE as Z,r,u as ee,y as te,j as ae,w as se,f as A,d as k,o as b,c as _,b as C,m as d,aR as x}from"./index-DLKlSkMo.js";import{S as oe,d as L,f as ne}from"./tools-B0tc7tWY.js";import{u as le}from"./measurementDefinition-4fZ_1X4C.js";import{u as ie}from"./devTree-BwbHtyaz.js";import{M as re}from"./index-BJVFa-Ub.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./ActionButton-DDquCBiw.js";import"./styleChecker-tOiS7TSB.js";import"./index-bX4hVoaz.js";import"./initDefaultProps-DBdyDbXv.js";import"./shallowequal-C1uyuyZ_.js";import"./index-W_N8ii3N.js";import"./index-DzeUeE5s.js";import"./index-C5qlGDhC.js";import"./index-pJrpCCGr.js";import"./index-Big_Av0Y.js";/* empty css                                                              */const ue=J("configMeasurementPlan",{state:()=>({measSolutionList:[]}),actions:{reset(){this.$reset()},async fetchGetMeasSolutionList(u){try{const t=await Z(u);return t&&t.length>0&&(this.measSolutionList=t),t}catch(t){throw console.error("获取失败:",t),t}},async fetchBatchAddMeasSolutions(u){try{return await Y(u)}catch(t){throw console.error(t),t}},async fetchEditMeasSolution(u){try{return await X(u)}catch(t){throw console.error(t),t}},async fetchBatchDeleteMeasSolutions(u){try{return await Q(u)}catch(t){throw console.error(t),t}}}}),ce={key:2},Le={__name:"measurementPlan",setup(u){const t=ue(),m=le(),W=U(),q=ie();let h=320;const y=(e={isform:!1})=>[{title:"测量方案",dataIndex:"measSolutionName",formItemWidth:h,columnWidth:200,isrequired:!0},{title:"测量方案类型",dataIndex:"measSolutionType",formItemWidth:h,columnWidth:200,isrequired:!0,inputType:"select",selectOptions:[{label:"传动链",value:"传动链",text:"传动链"},{label:"叶片",value:"叶片",text:"叶片"},{label:"塔筒",value:"塔筒",text:"塔筒"}],headerOperations:{filters:[]}},{title:"测量定义",dataIndex:"measDefinitionIDList",formItemWidth:h,columnWidth:200,isrequired:!0,inputType:"select",selectOptions:[],mode:"multiple",...e&&e.isform?{}:{customRender:({record:a})=>a.measDefinitions&&a.measDefinitions.length?a.measDefinitions.map(o=>o.key).join(","):""}},{title:"波形采集间隔(分)",dataIndex:"waveInterval",formItemWidth:h,columnWidth:150,validateRules:L({type:"number",title:"波形采集间隔",required:!0})},{title:"特征值采集间隔(分)",dataIndex:"eigenInterval",formItemWidth:h,columnWidth:150,validateRules:L({type:"number",title:"特征值采集间隔",required:!0})}],g=r(!1),w=r(!1),T=ee(),v=r(""),c=r(""),B=r(),f=r({}),I=r(""),i=r(T.params.id),n=te({tableColumns:y(),tableData:[],batchApplyData:[],batchApplyKey:"",bathApplyResponse1:{}}),p=r([]),O=async e=>{I.value&&await W.fetchDevTreedDevicelist({windParkID:I.value,useTobath:!0})},P=()=>{let e=q.findAncestorsWithNodes(i.value);e&&e.length&&e.length>1&&(I.value=e[e.length-2].id)},D=async e=>{g.value=!0,n.tableData=await t.fetchGetMeasSolutionList({WindTurbineID:i.value}),g.value=!1,n.tableColumns=y()},E=ae(()=>c.value==="batchAdd"?"1200px":"600px");se(()=>T.params.id,async e=>{e&&(t.reset(),m.reset(),i.value=e,P(),await O(),D())},{immediate:!0});const M=()=>{w.value=!0},S=e=>{w.value=!1,p.value=[],c.value="",v.value="",f.value={}},F=e=>{const{title:s,operateType:a}=e;c.value=a,v.value="批量增加测量方案",p.value=[...y({isform:!0})],R(),M()},N=async e=>{const{selectedkeys:s}=e;if(!s||!s.length){d.warning("请选择要删除的行");return}let a=[];for(let l=0;l<s.length;l++)a.push({windTurbineID:i.value,measSolutionID:s[l]});const o=await t.fetchBatchDeleteMeasSolutions({sourceData:a,targetTurbineIds:n.batchApplyData});o&&o.code===1?(D(),n.bathApplyResponse1=o.batchResults||{},d.success("删除成功")):d.error("删除失败:"+o.msg)},G=e=>{const{rowData:s,title:a,operateType:o}=e;c.value=o,f.value={...s,measDefinitionIDList:s.measDefinitions&&s.measDefinitions.length?s.measDefinitions.map(l=>l.value):[]},v.value="编辑测量方案",p.value=[...y({isform:!0})],R(),M()},R=async e=>{let s=p.value;(!m.measdList||!m.measdList.length)&&await m.fetchGetMeasdList({turbineID:i.value});let a=m.measdList,o=[];a&&a.length&&a.map(l=>{l.mdf_Ex&&l.mdf_Ex.modelType==0&&o.push({value:l.measDefinitionID,label:l.measDefinitionName})}),s[2].selectOptions=o},K=async e=>{let s={measSolutionID:f.value.measSolutionID,...e,daqInterval:f.value.daqInterval,WindTurbineID:i.value},a=await t.fetchEditMeasSolution({sourceData:s,targetTurbineIds:n.batchApplyData});a&&a.code===1?(D(),n.bathApplyResponse1=a.batchResults||{},d.success("提交成功"),S()):d.error("提交失败:"+a.msg)},j=async e=>{let s=ne(e);if(s&&s.length){let a=s.map((l,de)=>({...l,daqInterval:0,windTurbineID:i.value})),o=await t.fetchBatchAddMeasSolutions({sourceData:a,targetTurbineIds:n.batchApplyData});o&&o.code===1?(D(),n.bathApplyResponse1=o.batchResults||{},d.success("提交成功"),S()):d.error("提交失败:"+o.msg)}},z=async e=>{e.type&&e.type=="close"?(n.batchApplyData=[],n.batchApplyKey="",n[`bathApplyResponse${e.key}`]={}):(n.batchApplyData=e.turbines,n.batchApplyKey=e.key)};return x("deviceId",i),x("bathApplySubmit",z),(e,s)=>{const a=re,o=oe;return b(),A(o,{spinning:g.value,size:"large"},{default:k(()=>[(b(),_("div",{key:i.value},[C(V,{ref:"table",size:"default","table-key":"0","table-title":"测量方案","table-columns":n.tableColumns,borderLight:n.batchApplyKey=="0",bathApplyResponse:n.bathApplyResponse1,"table-operate":["edit","delete","add","batchDelete","batchAdd"],"record-key":"measSolutionID","table-datas":n.tableData,onAddRow:F,onDeleteRow:N,onEditRow:G},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"]),C(a,{maskClosable:!1,width:E.value,open:w.value,title:v.value,footer:"",onCancel:S},{default:k(()=>[c.value==="add"||c.value==="edit"?(b(),A($,{key:0,titleCol:p.value,form:B.value,initFormData:f.value,onSubmit:K},null,8,["titleCol","form","initFormData"])):c.value==="batchAdd"?(b(),A(H,{key:1,ref:"table",size:"default","table-key":"0","table-columns":p.value,"table-operate":["copyUp","delete"],"table-datas":[],"noCopyUp-keys":["measSolutionName","measDefinitionIDList"],onSubmit:j,onCancel:S},null,8,["table-columns"])):(b(),_("div",ce))]),_:1},8,["width","open","title"])]))]),_:1},8,["spinning"])}}};export{Le as default};
