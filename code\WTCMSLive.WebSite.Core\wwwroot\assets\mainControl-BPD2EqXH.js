import{u as oe,c as re,W as ne}from"./table-C0LB8EJD.js";import{O as le}from"./index-ChYhAQZ_.js";import{W as ie}from"./index-ZdxrKWAB.js";import{r as d,u as ce,y as pe,j as G,w as ue,f as D,d as f,o as y,c as w,b as R,i as de,F as ye,e as be,g as K,t as me,m as b,aR as N}from"./index-DLKlSkMo.js";import{u as fe}from"./configMainControl-DnfqUMMm.js";import{u as he}from"./devTree-BwbHtyaz.js";import{d as O,S as ge,f as ve}from"./tools-B0tc7tWY.js";import{D as Ie,a as Te}from"./index-CMmDaUgW.js";import{B as De}from"./index-bX4hVoaz.js";import{M as Ae}from"./index-BJVFa-Ub.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./ActionButton-DDquCBiw.js";import"./styleChecker-tOiS7TSB.js";import"./initDefaultProps-DBdyDbXv.js";import"./shallowequal-C1uyuyZ_.js";import"./index-W_N8ii3N.js";import"./index-DzeUeE5s.js";import"./index-C5qlGDhC.js";import"./index-pJrpCCGr.js";import"./index-Big_Av0Y.js";/* empty css                                                              */const we={class:"border"},Re={key:1,class:"nodata"},Oe={key:2},Je={__name:"mainControl",setup(Se){const t=fe(),V=he(),z=oe(),A=(e={idForm:!1})=>[{title:"寄存器地址",dataIndex:"channelNumber",columnWidth:180,formItemWidth:u,isrequired:!0,validateRules:O({title:"寄存器地址",type:"number"}),columnOperate:{type:"number"}},{title:"工况测量位置",dataIndex:"paramMeaning",columnWidth:160,formItemWidth:u,inputType:"select",selectOptions:[],labelInValue:!0,isrequired:!0},{title:"寄存器类型",dataIndex:"registerType",columnWidth:110,formItemWidth:u,inputType:"select",selectOptions:[],isrequired:!0,headerOperations:{filters:t.stateRegisterTypeOptions},...e.idForm?{}:{customRender:({text:s,record:r})=>{const o=t.stateRegisterTypeOptions.find(n=>n.value==r.registerType);return o?o.label:s}}},{title:"数据存储方式",dataIndex:"registerStorageType",columnWidth:110,formItemWidth:u,inputType:"select",selectOptions:[],isrequired:!0,headerOperations:{filters:t.registerStorageOptions},...e.idForm?{}:{customRender:({text:s,record:r})=>{const o=t.registerStorageOptions.find(n=>n.value==r.registerStorageType);return o?o.label:s}}},{title:"对齐方式",dataIndex:"byteArrayType",columnWidth:100,formItemWidth:u,inputType:"select",selectOptions:[],isrequired:!0,headerOperations:{filters:t.byteArrayTypeOptions},...e.idForm?{}:{customRender:({text:s,record:r})=>{const o=t.byteArrayTypeOptions.find(n=>n.value==r.byteArrayType);return o?o.label:s}}},{title:"字节交换",dataIndex:"byteSwap",columnWidth:120,formItemWidth:u,isrequired:!0,headerOperations:{filters:[{text:"是",value:1},{text:"否",value:0}]},initValue:0,inputType:"radio",selectOptions:[{label:"是",value:1},{label:"否",value:0}],...e.idForm?{}:{customRender:({text:s,record:r})=>s?"是":"否"}},{title:"转换系数",dataIndex:"coeff",formItemWidth:u,validateRules:O({title:"转换系数",type:"number"})}],M=async()=>{(!t.byteArrayTypeOptions||!t.byteArrayTypeOptions.length)&&await t.fetchGetByteArrayTypes()},F=ce();let u=320;const S=d(!1),I=d(""),p=d(""),h=d(""),i=d(F.params.id),g=d({}),m=d([]),P=d(A()),_=d(!1),a=pe({baseInfo:{},tableDatas:[],batchApplyData:[],batchApplyKey:"",bathApplyResponse1:{}}),E=async e=>{h.value&&await z.fetchDevTreedDevicelist({windParkID:h.value,useTobath:!0})},$=G(()=>[{label:"主控IP",value:a.baseInfo.mcsip||""},{label:"端口号",value:a.baseInfo.mcsPort||""}]),x=async()=>{const e=await t.fetchMCSGetMCSInfoList({windParkID:h.value,turbineID:i.value});e&&e.length>0?a.baseInfo=e[0]:a.baseInfo={}},k=async e=>{S.value=!0,await M(),await B(),await L(),W(),P.value=A(),h.value&&x(),S.value=!1},W=async e=>{if(h.value){const s=await t.fetchGetMCSGetMCSData({windTurbineID:i.value});a.tableDatas=s}},j=()=>{let e=V.findAncestorsWithNodes(i.value);e&&e.length&&e.length>1&&(h.value=e[e.length-2].id)};ue(()=>F.params.id,async e=>{e&&(t.reset(),i.value=e,j(),await E(),k())},{immediate:!0});const U=G(()=>p.value==="batchAdd"?"1200px":"600px"),H=()=>{p.value="editInfo",I.value="编辑主控信息",m.value=te,g.value=a.baseInfo,C()},C=()=>{_.value=!0},T=e=>{_.value=!1,m.value=[],g.value={},p.value="",I.value=""},J=async()=>{(!t.workFromMcsOptions||!t.workFromMcsOptions.length)&&await t.fetchGetWorkFromMcs({trubineId:i.value})},B=async()=>{(!t.stateRegisterTypeOptions||!t.stateRegisterTypeOptions.length)&&await t.fetchGetStateRegisterTypes()},L=async()=>{(!t.registerStorageOptions||!t.registerStorageOptions.length)&&await t.fetchGetRegisterStorages()},q=async()=>{const e=m.value;await J(),await B(),await L(),await M(),e[1].selectOptions=t.workFromMcsOptions,e[2].selectOptions=t.stateRegisterTypeOptions,e[3].selectOptions=t.registerStorageOptions,e[4].selectOptions=t.byteArrayTypeOptions},Q=async e=>{const{title:s,operateType:r}=e;p.value=r,I.value="添加"+s.split("列表")[0],m.value=A({idForm:!0}),await q(),C()},X=async e=>{const{tableKey:s,selectedkeys:r,deleteInRow:o,record:n}=e;if(!e||!r||!r.length)return;let l=[];if(o)l.push({measLocProcessID:n.measLocProcessID,windTurbineID:i.value});else for(let c=0;c<r.length;c++){let se=r[c].split("&&");l.push({measLocProcessID:se[1],windTurbineID:i.value})}const v=await t.fetchBatchDeleteMCSRegister({sourceData:l,targetTurbineIds:a.batchApplyData});v&&v.code===1?(W(),a.bathApplyResponse1=v.batchResults||{},b.success("删除成功")):b.error("删除失败:"+v.msg)},Y=async e=>{const{rowData:s,tableKey:r,title:o,operateType:n}=e;p.value=n,I.value="编辑"+o.split("列表")[0],g.value={...s,paramMeaning:{label:s.paramMeaning,value:s.measLocProcessID}};let l=[...A({idForm:!0})];l[1].disabled=!0,m.value=l,await q(),C()},Z=async e=>{if(p.value==="editInfo"){const o=await t.fetchBatchEditMCS([{...e,WindTurbineID:i.value}]);o&&o.code===1?(x(),T(),b.success("提交成功")):b.error("提交失败:"+o.msg);return}let s={...e,windTurbineID:i.value,measLocProcessID:g.value.paramMeaning.value,paramMeaning:g.value.paramMeaning.label,coeff:e.coeff!==void 0&&e.coeff!==null&&parseFloat(e.coeff)||0,byteArrayType:e.byteArrayType!==void 0&&e.byteArrayType!==null&&parseInt(e.byteArrayType)||0};const r=await t.fetchBatchAddEditMCSRegister({sourceData:[s],targetTurbineIds:a.batchApplyData});r&&r.code===1?(k(),a.bathApplyResponse1=r.batchResults||{},T(),b.success("提交成功")):b.error("提交失败:"+r.msg)},ee=async e=>{let s={windTurbineID:i.value},o=ve(e,s,{paramMeaning:{label:"paramMeaning",value:"measLocProcessID"}});o&&o.length>0&&(o=o.map(l=>({...l,coeff:l.coeff!==void 0&&l.coeff!==null&&parseFloat(l.coeff)||0,byteArrayType:l.byteArrayType!==void 0&&l.byteArrayType!==null&&parseInt(l.byteArrayType)||0})));let n=await t.fetchBatchAddMCSRegister({sourceData:o,targetTurbineIds:a.batchApplyData});n&&n.code===1?(k(),a.bathApplyResponse1=n.batchResults||{},T(),b.success("提交成功")):b.error("提交失败:"+n.msg)},te=[{title:"主控IP",dataIndex:"mcsip",formItemWidth:u,validateRules:O({type:"ip",title:"主控IP",required:!0})},{title:"端口号",dataIndex:"mcsPort",formItemWidth:u,validateRules:O({type:"port",title:"端口号",required:!0})}],ae=async e=>{e.type&&e.type=="close"?(a.batchApplyData=[],a.batchApplyKey="",a[`bathApplyResponse${e.key}`]={}):(a.batchApplyData=e.turbines,a.batchApplyKey=e.key)};return N("deviceId",i),N("bathApplySubmit",ae),(e,s)=>{const r=De,o=Te,n=Ie,l=Ae,v=ge;return y(),D(v,{spinning:S.value,size:"large"},{default:f(()=>[(y(),w("div",{key:i.value},[R(re,{tableTitle:"主控信息",defaultCollapse:!0,batchApply:!1},{rightButtons:f(()=>[R(r,{type:"primary",onClick:s[0]||(s[0]=c=>H()),disabled:!(a.baseInfo&&a.baseInfo.mcsip&&a.baseInfo.mcsip!=="")},{default:f(()=>s[1]||(s[1]=[K(" 编辑 ",-1)])),_:1,__:[1]},8,["disabled"])]),content:f(()=>[de("div",we,[R(n,{column:4,size:"small"},{default:f(()=>[(y(!0),w(ye,null,be($.value,c=>(y(),D(o,{label:c.label,key:c.label},{default:f(()=>[K(me(c.value),1)]),_:2},1032,["label"]))),128))]),_:1})])]),_:1}),a.baseInfo&&a.baseInfo.mcsip&&a.baseInfo.mcsip!==""?(y(),D(ne,{key:0,ref:"table",size:"default",borderLight:a.batchApplyKey=="1",bathApplyResponse:a.bathApplyResponse1,"table-key":"1","table-title":"主控数据寄存器列表","table-columns":P.value,"table-operate":["delete","edit","batchDelete","batchAdd"],recordKey:c=>`${c.channelNumber}&&${c.measLocProcessID}`,"table-datas":a.tableDatas,onAddRow:Q,onDeleteRow:X,onEditRow:Y},null,8,["borderLight","bathApplyResponse","table-columns","recordKey","table-datas"])):(y(),w("div",Re," 请添加主控!")),R(l,{maskClosable:!1,width:U.value,open:_.value,title:I.value,footer:"",onCancel:T},{default:f(()=>[p.value==="add"||p.value==="editInfo"||p.value==="edit"?(y(),D(le,{key:0,titleCol:m.value,initFormData:g.value,onSubmit:Z},null,8,["titleCol","initFormData"])):p.value==="batchAdd"?(y(),D(ie,{key:1,ref:"table",size:"default","table-key":"0","table-columns":m.value,"table-operate":["copyUp","delete"],"table-datas":[],"noRepeat-Keys":["channelNumber"],"noCopyUp-keys":["measLocProcessID"],onSubmit:ee,onCancel:T},null,8,["table-columns"])):(y(),w("div",Oe))]),_:1},8,["width","open","title"])]))]),_:1},8,["spinning"])}}};export{Je as default};
