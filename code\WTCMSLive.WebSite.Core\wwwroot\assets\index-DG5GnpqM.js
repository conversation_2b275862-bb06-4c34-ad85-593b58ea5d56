import{r as v,u as de,y as pe,j as be,w as me,f as z,d as w,o as A,i as R,b as C,c as k,F as Z,e as ee,q as he,t as te,bN as ye,g as ae,m,aR as le}from"./index-DLKlSkMo.js";import{u as De,c as ve,W as G}from"./table-C0LB8EJD.js";import{O as fe}from"./index-ChYhAQZ_.js";import{W as Ie}from"./index-ZdxrKWAB.js";import{d as y,S as Ae,f as H}from"./tools-B0tc7tWY.js";import{u as ge}from"./collectionUnitConfig-BYxKHJs0.js";import{u as Le}from"./devTree-BwbHtyaz.js";import{_ as Re}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{D as Ce,a as Te}from"./index-CMmDaUgW.js";import{B as _e}from"./index-bX4hVoaz.js";import{M as we}from"./index-BJVFa-Ub.js";import"./ActionButton-DDquCBiw.js";import"./styleChecker-tOiS7TSB.js";import"./initDefaultProps-DBdyDbXv.js";import"./shallowequal-C1uyuyZ_.js";import"./index-W_N8ii3N.js";import"./index-DzeUeE5s.js";import"./index-C5qlGDhC.js";import"./index-pJrpCCGr.js";import"./index-Big_Av0Y.js";/* empty css                                                              */const S=(l={size:"large"})=>[{align:"center",title:"通道编号",dataIndex:"channelNumber",isrequired:!0,selectOptions:[],inputType:"select",disabled:l&&l.edit,columnWidth:l&&l.size=="small"?50:160},{title:"测量位置",dataIndex:l&&l.edit?"measLocVibName":"MeasLocVibID",align:"center",isrequired:!0,selectOptions:[],inputType:"select",columnWidth:l&&l.size=="small"?160:200,disabled:l&&l.edit,...l&&l.isform?{}:{customRender:({record:_})=>_.measLocVibName||""}},{align:"center",title:"灵敏度系数(mv/(m/s^2))",dataIndex:"coeff_L0",isrequired:!0,columnWidth:l&&l.size=="small"?50:160},{align:"center",title:"最低偏执电压(V)",dataIndex:"minBiasVolt",isrequired:!0,columnWidth:l&&l.size=="small"?50:160},{align:"center",title:"最高偏执电压(V)",dataIndex:"maxBiasVolt",columnWidth:l&&l.size=="small"?50:160,validateRules:y({type:"number",title:"最高偏执电压",required:!0})}],K=320,T=l=>{let s=[{align:"center",title:"采集单元名称",dataIndex:"dauName",isrequired:!0,formItemWidth:K},{align:"center",title:"IP地址",dataIndex:"ip",formItemWidth:K,validateRules:y({type:"ip",title:"IP地址",required:!0})},{align:"center",title:"采集间隔(分钟)",dataIndex:"dataAcquisitionInterval",isrequired:!0,formItemWidth:K,validateRules:y({type:"number",title:"采集间隔",required:!0})},{align:"center",title:"状态",dataIndex:"isAvailable",inputType:"checkbox",selectOptions:[{label:"启用",value:!0}],formItemWidth:K},{align:"center",title:"端口",dataIndex:"port",formItemWidth:K,validateRules:y({type:"port",title:"端口",required:!0})}],_=[...S({...l})],$=[{align:"center",title:"通道编号",dataIndex:"channelNumber",isrequired:!0,selectOptions:[],inputType:"select",disabled:l&&l.edit},{title:"测量位置",dataIndex:l&&l.edit?"measLocVibName":"measLoc_ProcessId",align:"center",isrequired:!0,selectOptions:[],inputType:"select",disabled:l&&l.edit,...l&&l.isform?{}:{customRender:({record:p})=>p.measLocVibName||""}},{align:"center",title:"转换系数A",dataIndex:"coeff_a",validateRules:y({type:"number",title:"转换系数A",required:!0})},{align:"center",title:"转换系数B",dataIndex:"coeff_b",validateRules:y({type:"number",title:"转换系数B",required:!0})}],P=[{align:"center",title:"通道编号",dataIndex:"channelNumber",isrequired:!0,selectOptions:[],inputType:"select"},{title:"测量位置",dataIndex:l&&l.edit?"measLocRotSpdName":"measLocRotSpdID",align:"center",isrequired:!0,disabled:l&&l.edit,selectOptions:[],inputType:"select",...l&&l.isform?{}:{customRender:({record:p})=>p.measLocRotSpdName||""}}],q=[...S({size:"small"}),{align:"center",title:"物理量类型",inputType:"select",selectOptions:[],dataIndex:"upperLimitFreqency"},{align:"center",title:"应变转应力系数A",dataIndex:"upperLimitFreqency",validateRules:y({type:"number",title:"应变转应力系数A",required:!0})},{align:"center",title:"应变转应力系数B",dataIndex:"upperLimitFreqency",validateRules:y({type:"number",title:"应变转应力系数B",required:!0})},{align:"center",title:"寄存器地址",dataIndex:"upperLimitFreqency"}];[...S({size:"small"}),y({type:"number",title:"应变转应力系数A",required:!0}),y({type:"number",title:"应变转应力系数B",required:!0}),y({type:"number",title:"初始安装间距",required:!0})];let I=[...S({size:"small"}),{align:"center",title:"法兰",dataIndex:"upperLimitFreqency"},{align:"center",title:"分配器号",dataIndex:"upperLimitFreqency"},{align:"center",title:"分配器号通道",dataIndex:"upperLimitFreqency"},{align:"center",title:"测量基准",dataIndex:"upperLimitFreqency"},{align:"center",title:"预紧力系数",dataIndex:"upperLimitFreqency"},{align:"center",title:"温度系数",dataIndex:"upperLimitFreqency"}],x=[...S({size:"small"}),{align:"center",title:"法兰",dataIndex:"upperLimitFreqency"},{align:"center",title:"测量基准",dataIndex:"upperLimitFreqency"},{align:"center",title:"预紧力系数",dataIndex:"upperLimitFreqency"},{align:"center",title:"温度系数",dataIndex:"upperLimitFreqency"}],D=[{align:"center",title:"通道编号",dataIndex:"channelNumber",isrequired:!0,selectOptions:[],inputType:"select"},{title:"测量位置",dataIndex:l&&l.edit?"measLocName":"measLoc_ProcessId",align:"center",isrequired:!0,disabled:l&&l.edit,selectOptions:[],inputType:"select",...l&&l.isform?{}:{customRender:({record:p})=>p.measLocName||""}},{align:"center",title:"转换系数A",dataIndex:"powerCoeff_a",validateRules:y({type:"number",title:"转换系数A",required:!0}),...l&&l.isform?{}:{customRender:({record:p})=>p.coeff_a}},{align:"center",title:"转换系数B",dataIndex:"powerCoeff_b",validateRules:y({type:"number",title:"转换系数B",required:!0}),...l&&l.isform?{}:{customRender:({record:p})=>p.coeff_b}}],b=[];if(l&&(l.dauType||l.dauType==0))switch(l.dauType){case 0:case 6:b=_;break;case 1:b=I;break;case 2:b=q;break;case 7:b=x;break;default:b=_;break}else b=_;return[s,b,$,P,D]},ke={class:"btnGroups"},qe={class:"clearfix"},xe=["onClick"],Ne={class:"border"},Ue={class:"tableItems"},We={class:"tableItems"},Fe={class:"tableItems"},Se={class:"tableItems"},Ke={key:1,class:"nodata"},Pe={key:2},Be={__name:"index",setup(l){const s=ge(),_=Le(),$=De(),P=de(),q=v(!1),I=v(""),x=v(!1),D=v(""),b=v({}),p=v([]),o=v({}),g=v(""),B=v(),d=v(P.params.id),J=v([]),j=v({}),e=pe({noCopyUpKeys:["channelNumber","MeasLocVibID","measLoc_ProcessId"],removeDuplicateKeys:["channelNumber","MeasLocVibID","measLoc_ProcessId"],dauList:[],table1Columns:[],table2Columns:T()[2],table3Columns:T()[3],table4Columns:T()[4],table1Data:[],table2Data:[],table3Data:[],table4Data:[],batchApplyKey:"",bathApplyResponse1:[],bathApplyResponse2:[],bathApplyResponse3:[],bathApplyResponse4:[]}),se=be(()=>g.value==="batchAdd"?"1200px":"600px"),ie=async t=>{B.value&&await $.fetchDevTreedDevicelist({windParkID:B.value,useTobath:!0})},ne=()=>{let t=_.findAncestorsWithNodes(d.value);t&&t.length&&t.length>1&&(B.value=t[t.length-2].id)},Q=async t=>{if(q.value=!0,e.dauList=await s.fetchGetDAUList({WindTurbineID:d.value,WindParkId:B.value}),q.value=!1,s.dAUList&&s.dAUList.length){if(t&&t=="edit"&&JSON.stringify(o.value)!=="{}"){let a=s.dAUList.find(r=>r.dauID==o.value.dauID);o.value=a}else o.value=s.dAUList[0],e.table1Columns=T({dauType:o.value.dauType})[1],U(),W(),M(),F();E(o.value)}else o.value={},E({}),e.table1Columns=T()[1],e.table1Data=[],e.table2Data=[],e.table3Data=[],e.table4Data=[]};me(()=>P.params.id,async t=>{t&&(s.reset(),d.value=t,ne(),await ie(),Q())},{immediate:!0});const E=t=>{j.value=t,J.value=[{label:"采集单元名称",value:t.dauName},{label:"IP地址",value:t.ip},{label:"采集间隔(分钟)",value:t.dataAcquisitionInterval},{label:"状态",value:t.isAvailable?"开启":"禁用"},{label:"端口",value:t.port}]},V=t=>{const{title:a,operateType:r,tableKey:c}=t;p.value=T({isForm:!0,dauType:o.value.dauType})[c];let u=a.substring(0,a.length-2);I.value=r=="add"?"增加"+u:"批量增加"+u,g.value=r,D.value=c,X(c),Y()},N=t=>{const{tableKey:a,rowData:r}=t;let c=T({isForm:!0,edit:!0,dauType:o.value.dauType})[a];p.value=[...c],D.value=a,g.value="edit",a=="0"?(I.value="编辑采集单元信息",b.value={...r,isAvailable:r.isAvailable?[!0]:[!1]}):a=="2"?(I.value="编辑电流电压通道信息",b.value={...r,measLoc_ProcessId:r.measLocVibID}):a=="4"?(I.value="编辑工况通道信息",b.value={...r,powerCoeff_a:r.coeff_a,powerCoeff_b:r.coeff_b}):(b.value={...r},a=="1"?I.value="编辑振动通道信息":a=="3"&&(I.value="编辑转速通道信息",X(a))),Y()},O=async t=>{const{tableKey:a,selectedkeys:r,record:c}=t;D.value=a;let u=[];if(c)a=="1"||a=="2"?u.push({ChannelNumber:c.channelNumber,MeasLocationID:c.measLocVibID}):a=="3"?u.push({ChannelNumber:c.channelNumber,MeasLocationID:c.measLocRotSpdID}):a=="4"&&u.push({ChannelNumber:c.channelNumber,MeasLocationID:c.measLoc_ProcessId});else for(let n=0;n<r.length;n++){let f=r[n].split("&&");u.push({MeasLocationID:f[1],ChannelNumber:f[0]})}let h={sourceData:{WindTurbineID:d.value,DauID:o.value.dauID,Channels:u},targetTurbineIds:e.batchApplyData},i={};a=="1"?i=await s.fetchBatchDeleteVibChannels(h):a=="2"?i=await s.fetchBatchDeleteProcessChannels(h):a=="3"?i=await s.fetchBatchDeleteRotSpeedChannels(h):a=="4"&&(i=await s.fetchBatchDeleteWorkConditionChannels(h)),i&&i.code===1?(a=="1"?(U(),e.bathApplyResponse1=i.batchResults||{}):a=="2"?(W(),e.bathApplyResponse2=i.batchResults||{}):a=="3"?(M(),e.bathApplyResponse3=i.batchResults||{}):a=="4"&&(F(),e.bathApplyResponse4=i.batchResults||{}),m.success("删除成功")):m.error("删除失败:"+i.msg)},X=async t=>{const a=p.value;switch(await s.fetchGetDAUVibChannelNumList({WindTurbineID:d.value,DAUID:o.value.dauID}),a[0].selectOptions=s.channelNumList,t){case"1":await s.fetchGetmeasLocList({WindTurbineID:d.value,DAUID:o.value.dauID}),a[1].selectOptions=s.measLocList,p.value=[...a];break;case"2":await s.fetchGetmeasLocProcessList({WindTurbineID:d.value,DAUID:o.value.dauID}),a[1].selectOptions=s.measLocProcessList,p.value=[...a];break;case"3":await s.fetchGetRotSpdMeasLocList({WindTurbineID:d.value,DAUID:o.value.dauID}),a[1].selectOptions=s.rotSpdMeasLocList,p.value=[...a];break;case"4":await s.fetchGetWordConditionMeasLoc({WindTurbineID:d.value,DAUID:o.value.dauID}),a[1].selectOptions=s.wordConditionMeasLocList,p.value=[...a];break}},oe=async t=>{switch(D.value){case"0":let a={...b.value,...t,isAvailable:t.isAvailable&&t.isAvailable.length?t.isAvailable[0]:!1,windParkID:b.value.windParkID,dataAcquisitionInterval:t.dataAcquisitionInterval?t.dataAcquisitionInterval*1:0};const r=await s.fetchDAUEditDAU(a);r&&r.code===1?(Q("edit"),L(),m.success("提交成功")):m.error("提交失败:"+r.msg);break;case"1":case"2":let c={...b.value,...t,DauID:o.value.dauID,WindTurbineID:d.value},u={};D.value=="1"?u=await s.fetchBatchEditVibChannels({sourceData:c,targetTurbineIds:e.batchApplyData}):D.value=="2"&&(u=await s.fetchBatchEditProcessChannel({sourceData:c,targetTurbineIds:e.batchApplyData})),u&&u.code===1?(D.value=="1"?(U(),e.bathApplyResponse1=u.batchResults||{}):D.value=="2"&&(W(),e.bathApplyResponse2=u.batchResults||{}),L(),m.success("提交成功")):m.error("提交失败:"+u.msg);break;case"3":let h={channels:[t],WindTurbineID:d.value,DAUID:o.value.dauID},i={};g.value=="add"?i=await s.fetchAddRotSpeedChannels({sourceData:h,targetTurbineIds:e.batchApplyData}):i=await s.fetchEditRotSpeedChannel({sourceData:h,targetTurbineIds:e.batchApplyData}),i&&i.code===1?(M(),e.bathApplyResponse3=i.batchResults||{},L(),m.success("提交成功")):m.error("提交失败:"+i.msg);break;case"4":let n={...b.value,...t,WindTurbineID:d.value,DAUID:o.value.dauID};const f=await s.fetchEditWorkConditionChannel({sourceData:n,targetTurbineIds:e.batchApplyData});f&&f.code===1?(F(),e.bathApplyResponse4=f.batchResults||{},L(),m.success("提交成功")):m.error("提交失败:"+f.msg);break}},re=async t=>{switch(D.value){case"1":let a=H(t);if(a&&a.length){let u=a.map((n,f)=>({...n})),h={WindTurbineID:d.value,DAUID:o.value.dauID,Channels:u},i=await s.fetchBatchAddVibChannels({sourceData:h,targetTurbineIds:e.batchApplyData});i&&i.code===1?(e.bathApplyResponse1=i.batchResults||{},U(),m.success("提交成功"),L()):m.error("提交失败:"+i.msg)}break;case"2":let r=H(t);if(r&&r.length){let u=r.map((n,f)=>({...n})),h={WindTurbineID:d.value,DAUID:o.value.dauID,Channels:u},i=await s.fetchBatchAddProcessChannels({sourceData:h,targetTurbineIds:e.batchApplyData});i&&i.code===1?(e.bathApplyResponse2=i.batchResults||{},W(),m.success("提交成功"),L()):m.error("提交失败:"+i.msg)}break;case"4":let c=H(t);if(c&&c.length){let u=c.map((n,f)=>({...n})),h={WindTurbineID:d.value,DAUID:o.value.dauID,Channels:u},i=await s.fetchAddWorkConditionChannels({sourceData:h,targetTurbineIds:e.batchApplyData});i&&i.code===1?(e.bathApplyResponse4=i.batchResults||{},F(),m.success("提交成功"),L()):m.error("提交失败:"+i.msg)}break}},Y=()=>{x.value=!0},L=()=>{x.value=!1,b.value={},D.value="",I.value="",g.value="",p.value=[]},ue=t=>{t.dauID!=o.value.dauID&&(o.value=t,e.table1Columns=T({dauType:t.dauType})[1],E(t),U(),W(),M(),F())},U=async()=>{e.table1Data=await s.fetchDAUGetDAUVibList({WindTurbineID:d.value,DAUID:o.value.dauID})},W=async()=>{e.table2Data=await s.fetchGetDAUProcessList({WindTurbineID:d.value,DAUID:o.value.dauID})},M=async()=>{e.table3Data=await s.fetchGetRotSpeedList({WindTurbineID:d.value,DAUID:o.value.dauID})},F=async()=>{e.table4Data=await s.fetchGetworkConditionList({WindTurbineID:d.value,DAUID:o.value.dauID})},ce=async t=>{t.type&&t.type=="close"?(e.batchApplyData=[],e.batchApplyKey="",e[`bathApplyResponse${t.key}`]={}):(e.batchApplyData=t.turbines,e.batchApplyKey=t.key)};return le("deviceId",d),le("bathApplySubmit",ce),(t,a)=>{const r=_e,c=Te,u=Ce,h=we,i=Ae;return A(),z(i,{spinning:q.value,size:"large"},{default:w(()=>[a[2]||(a[2]=R("h1",null," 采集单元信息 ",-1)),R("div",ke,[R("ul",qe,[(A(!0),k(Z,null,ee(e.dauList,n=>(A(),k("li",{key:n.dauID,class:he({active:o.value.dauID===n.dauID}),onClick:f=>ue(n)},te(n.dauName),11,xe))),128))])]),C(ve,{tableTitle:"采集单元信息",defaultCollapse:!0,batchApply:!1},ye({content:w(()=>[R("div",Ne,[C(u,{column:5,size:"small"},{default:w(()=>[(A(!0),k(Z,null,ee(J.value,n=>(A(),z(c,{label:n.label,key:n.label},{default:w(()=>[ae(te(n.value),1)]),_:2},1032,["label"]))),128))]),_:1})])]),_:2},[o.value&&o.value.dauID?{name:"rightButtons",fn:w(()=>[C(r,{type:"primary",onClick:a[0]||(a[0]=n=>N({tableKey:"0",rowData:j.value,title:"测量定义"}))},{default:w(()=>a[1]||(a[1]=[ae(" 编辑 ",-1)])),_:1,__:[1]})]),key:"0"}:void 0]),1024),o.value&&o.value.dauID?(A(),k("div",{class:"blockBorder",key:d.value},[R("div",Ue,[C(G,{"table-key":"1","table-title":"振动通道列表","table-columns":e.table1Columns,borderLight:e.batchApplyKey=="1",bathApplyResponse:e.bathApplyResponse1,"table-operate":["edit","delete","batchAdd","batchDelete"],noPagination:!0,recordKey:n=>`${n.channelNumber}&&${n.measLocVibID}`,onAddRow:V,"table-datas":e.table1Data,onDeleteRow:O,onEditRow:N},null,8,["table-columns","borderLight","bathApplyResponse","recordKey","table-datas"])]),R("div",We,[C(G,{"table-key":"2","table-title":"电流电压通道列表","table-columns":e.table2Columns,borderLight:e.batchApplyKey=="2",bathApplyResponse:e.bathApplyResponse2,"table-operate":["edit","delete","batchAdd","batchDelete"],noPagination:!0,recordKey:n=>`${n.channelNumber}&&${n.measLocVibID}`,"table-datas":e.table2Data,onAddRow:V,onDeleteRow:O,onEditRow:N},null,8,["table-columns","borderLight","bathApplyResponse","recordKey","table-datas"])]),R("div",Fe,[C(G,{"table-key":"3","table-title":"转速通道列表","table-columns":e.table3Columns,noPagination:!0,borderLight:e.batchApplyKey=="3",bathApplyResponse:e.bathApplyResponse3,"table-operate":["delete",e.table3Data&&e.table3Data.length?"edit":"add","batchDelete"],recordKey:n=>`${n.channelNumber}&&${n.measLocRotSpdID}`,"table-datas":e.table3Data,onAddRow:V,onDeleteRow:O,onEditRow:N},null,8,["table-columns","borderLight","bathApplyResponse","table-operate","recordKey","table-datas"])]),R("div",Se,[C(G,{"table-key":"4","table-title":"工况通道列表","table-columns":e.table4Columns,noPagination:!0,borderLight:e.batchApplyKey=="4",bathApplyResponse:e.bathApplyResponse4,"table-operate":["edit","delete","batchAdd","batchDelete"],recordKey:n=>`${n.channelNumber}&&${n.measLoc_ProcessId}`,"table-datas":e.table4Data,onAddRow:V,onDeleteRow:O,onEditRow:N},null,8,["table-columns","borderLight","bathApplyResponse","recordKey","table-datas"])])])):(A(),k("div",Ke," 请先添加采集单元!")),C(h,{maskClosable:!1,width:se.value,open:x.value,title:I.value,footer:"",destroyOnClose:!0,onCancel:L},{default:w(()=>[g.value==="add"||g.value==="edit"||g.value==="editDevice"?(A(),z(fe,{key:0,ref:"formModalRef",titleCol:p.value,initFormData:b.value,onSubmit:oe},null,8,["titleCol","initFormData"])):g.value==="batchAdd"?(A(),z(Ie,{key:1,ref:"modalTableFormRef",size:"default","table-key":"0","table-columns":p.value,"table-operate":["copyUp","delete"],"table-datas":[],"noCopyUp-keys":e.noCopyUpKeys,onSubmit:re,removeDuplicateKeys:e.removeDuplicateKeys,"noRepeat-Keys":e.removeDuplicateKeys,onCancel:L},null,8,["table-columns","noCopyUp-keys","removeDuplicateKeys","noRepeat-Keys"])):(A(),k("div",Pe))]),_:1},8,["width","open","title"])]),_:1,__:[2]},8,["spinning"])}}},ot=Re(Be,[["__scopeId","data-v-09f3da54"]]);export{ot as default};
