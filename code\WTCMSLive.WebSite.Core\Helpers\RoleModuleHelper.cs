using CMSFramework.BusinessEntity;
using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Core.Models.DTOs;

namespace WTCMSLive.WebSite.Core.Helpers
{
    /// <summary>
    /// 角色模块管理辅助类
    /// </summary>
    public static class RoleModuleHelper
    {
        /// <summary>
        /// 获取所有模块列表
        /// </summary>
        /// <returns>模块列表</returns>
        public static List<ModuleDTO> GetAllModules()
        {
            var modules = new List<ModuleDTO>();

            try
            {
                using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
                {
                    var moduleList = ctx.SysModules.ToList();
                    foreach (var module in moduleList)
                    {
                        modules.Add(new ModuleDTO
                        {
                            ModuleID = module.ModuleID.ToString(),
                            ModuleName = module.ModuleName
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[GetAllModules]获取模块列表失败", ex);
            }

            return modules.OrderBy(m => Convert.ToInt32(m.ModuleID)).ToList();
        }

        /// <summary>
        /// 获取角色列表（包含绑定的模块）
        /// </summary>
        /// <returns>角色列表</returns>
        public static List<RoleDTO> GetRoleListWithModules()
        {
            var roles = new List<RoleDTO>();
            try
            {
                using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
                {
                    var roleList = ctx.SysRoles.ToList();

                    var roleModuleMappings = ctx.SysRoleModuleMapping.ToList();
                    var moduleList = ctx.SysModules.ToList();

                    foreach (var role in roleList)
                    {
                        var roleDto = new RoleDTO
                        {
                            RoleID = role.RoleID,
                            RoleName = role.RoleName,
                            RoleDescription = role.RoleDes ?? "",
                            IsSystemRole = role.IsSystemRole,
                            Modules = new List<ModuleDTO>()
                        };

                        // 获取角色绑定的模块
                        var roleMappings = roleModuleMappings.Where(rm => rm.RoleID == role.RoleID).ToList();
                        foreach (var mapping in roleMappings)
                        {
                            var module = moduleList.FirstOrDefault(m => m.ModuleID == mapping.ModuleID);
                            if (module != null)
                            {
                                roleDto.Modules.Add(new ModuleDTO
                                {
                                    ModuleID = module.ModuleID,
                                    ModuleName = module.ModuleName
                                });
                            }
                        }

                        roles.Add(roleDto);
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[GetRoleListWithModules]获取角色列表失败", ex);
            }

            return roles.OrderBy(r => r.RoleID).ToList();
        }

        /// <summary>
        /// 添加角色及其模块映射
        /// </summary>
        /// <param name="request">添加角色请求</param>
        public static void AddRoleWithModules(AddRoleRequestDTO request)
        {
            try
            {
                using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
                {
                    using (var transaction = ctx.Database.BeginTransaction())
                    {
                        try
                        {
                            // 生成新的角色ID
                            //var maxRoleId = ctx.SysRoles.Any() ? ctx.SysRoles.Max(r => r.RoleID) : "0";
                            int maxRoleId = ctx.SysRoles
                                           .AsEnumerable()
                                           .Max(r => int.Parse(r.RoleID));
                            var newRoleId = maxRoleId + 1;

                            // 添加角色
                            var role = new Role
                            {
                                RoleID = newRoleId.ToString(),
                                RoleName = request.RoleName,
                                RoleDes = request.RoleDescription ?? "",
                                IsSystemRole = false
                            };

                            ctx.SysRoles.Add(role);
                            ctx.SaveChanges();

                            // 添加角色模块映射
                            if (request.ModuleIds != null && request.ModuleIds.Any())
                            {
                                foreach (var moduleId in request.ModuleIds)
                                {
                                    var mapping = new RoleModuleMapping
                                    {
                                        RoleID = newRoleId.ToString(),
                                        ModuleID = moduleId
                                    };
                                    ctx.SysRoleModuleMapping.Add(mapping);
                                }
                                ctx.SaveChanges();
                            }

                            transaction.Commit();
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[AddRoleWithModules]添加角色失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 编辑角色及其模块映射
        /// </summary>
        /// <param name="request">编辑角色请求</param>
        public static void EditRoleWithModules(EditRoleRequestDTO request)
        {
            try
            {
                using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
                {
                    using (var transaction = ctx.Database.BeginTransaction())
                    {
                        try
                        {
                            var roleId = Convert.ToInt32(request.RoleID);
                            var role = ctx.SysRoles.FirstOrDefault(r => r.RoleID == roleId.ToString());
                            if (role == null)
                            {
                                throw new Exception("角色不存在");
                            }

                            // 更新角色信息
                            role.RoleName = request.RoleName;
                            role.RoleDes = request.RoleDescription ?? "";
                            ctx.SaveChanges();

                            // 删除原有的角色模块映射
                            var existingMappings = ctx.SysRoleModuleMapping.Where(rm => rm.RoleID == request.RoleID).ToList();
                            ctx.SysRoleModuleMapping.RemoveRange(existingMappings);
                            ctx.SaveChanges();

                            // 添加新的角色模块映射
                            if (request.ModuleIds != null && request.ModuleIds.Any())
                            {
                                foreach (var moduleId in request.ModuleIds)
                                {
                                    var mapping = new RoleModuleMapping
                                    {
                                        RoleID = request.RoleID,
                                        ModuleID = moduleId
                                    };
                                    ctx.SysRoleModuleMapping.Add(mapping);
                                }
                                ctx.SaveChanges();
                            }

                            transaction.Commit();
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditRoleWithModules]编辑角色失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 删除角色及其相关映射
        /// </summary>
        /// <param name="roleId">角色ID</param>
        public static void DeleteRoleWithMappings(string roleId)
        {
            try
            {
                using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
                {
                    using (var transaction = ctx.Database.BeginTransaction())
                    {
                        try
                        {
                            // 检查是否有用户使用此角色
                            var userRoleMappings = ctx.SysUserRoleMapping.Where(ur => ur.RoleID == roleId).ToList();
                            if (userRoleMappings.Any())
                            {
                                throw new Exception("该角色正在被用户使用，无法删除");
                            }

                            //// 删除角色模块映射
                            //var roleModuleMappings = ctx.SysRoleModuleMapping.Where(rm => rm.RoleID == roleId).ToList();
                            //ctx.SysRoleModuleMapping.RemoveRange(roleModuleMappings);

                            //// 删除角色功能映射（如果存在）
                            //var roleFunctionMappings = ctx.SysRoleFunctionMapping.Where(rf => rf.RoleID == roleId).ToList();
                            //ctx.SysRoleFunctionMapping.RemoveRange(roleFunctionMappings);

                            // 删除角色
                            var role = ctx.SysRoles.FirstOrDefault(r => r.RoleID == roleId);
                            if (role != null)
                            {
                                ctx.SysRoles.Remove(role);
                            }

                            ctx.SaveChanges();
                            transaction.Commit();
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[DeleteRoleWithMappings]删除角色失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 检查角色名称是否重复
        /// </summary>
        /// <param name="roleName">角色名称</param>
        /// <param name="excludeRoleId">排除的角色ID（编辑时使用）</param>
        /// <returns>是否重复</returns>
        public static bool IsRoleNameExists(string roleName, string excludeRoleId = null)
        {
            try
            {
                using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
                {
                    var query = ctx.SysRoles.Where(r => r.RoleName == roleName);

                    if (!string.IsNullOrEmpty(excludeRoleId))
                    {
                        var excludeId = Convert.ToInt32(excludeRoleId);
                        query = query.Where(r => r.RoleID != excludeId.ToString());
                    }

                    return query.Any();
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[IsRoleNameExists]检查角色名称失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取用户的角色和模块信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户角色和模块信息</returns>
        public static RoleDTO GetUserRoleAndModules(string userId)
        {
            var userRole = new RoleDTO();
            var userModules = new List<ModuleDTO>();

            try
            {
                using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
                {
                    // 获取用户角色映射
                    var userRoleMapping = ctx.SysUserRoleMapping.FirstOrDefault(ur => ur.UserID == userId);
                    if (userRoleMapping != null)
                    {
                        // 获取角色信息
                        var roleId = Convert.ToInt32(userRoleMapping.RoleID);
                        var role = ctx.SysRoles.FirstOrDefault(r => r.RoleID == roleId.ToString());
                        if (role != null)
                        {
                            userRole = new RoleDTO
                            {
                                RoleID = role.RoleID.ToString(),
                                RoleName = role.RoleName,
                                RoleDescription = role.RoleDes ?? "",
                                IsSystemRole = role.IsSystemRole
                            };

                            // 获取角色绑定的模块
                            List<RoleModuleMapping> roleModuleMappings = new List<RoleModuleMapping>();
                            if (userRole.IsSystemRole)
                            {
                                var mod = ctx.SysModules.ToList();
                                foreach(var m in mod)
                                {
                                    roleModuleMappings.Add(new RoleModuleMapping()
                                    {
                                        RoleID = userRole.RoleID,
                                        ModuleID = m.ModuleID,
                                    });
                                }
                            }
                            else
                            {
                                roleModuleMappings = ctx.SysRoleModuleMapping.Where(rm => rm.RoleID == userRoleMapping.RoleID).ToList();
                            }
                            
                            var moduleList = ctx.SysModules.ToList();

                            foreach (var mapping in roleModuleMappings)
                            {
                                var module = moduleList.FirstOrDefault(m => m.ModuleID.ToString() == mapping.ModuleID);
                                if (module != null)
                                {
                                    userModules.Add(new ModuleDTO
                                    {
                                        ModuleID = module.ModuleID.ToString(),
                                        ModuleName = module.ModuleName
                                    });
                                }
                            }

                            // 将模块信息也添加到角色对象中
                            userRole.Modules = userModules.ToList();
                        }
                    }

                    // 如果用户没有分配角色，返回默认信息
                    if (string.IsNullOrEmpty(userRole.RoleID))
                    {
                        userRole = new RoleDTO
                        {
                            RoleID = "0",
                            RoleName = "默认角色",
                            RoleDescription = "未分配角色的用户",
                            IsSystemRole = false,
                            Modules = new List<ModuleDTO>()
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[GetUserRoleAndModules]获取用户角色和模块信息失败", ex);

                // 返回默认角色信息
                userRole = new RoleDTO
                {
                    RoleID = "0",
                    RoleName = "默认角色",
                    RoleDescription = "获取角色信息失败",
                    IsSystemRole = false,
                    Modules = new List<ModuleDTO>()
                };
            }

            return userRole;
        }
    }
}
