using Microsoft.AspNetCore.Mvc;
using WTCMSLive.WebSite.Core.Models;
using WTCMSLive.WebSite.Core.Models.DTOs;
using WTCMSLive.WebSite.Core.Service;
using System.Text;
using Microsoft.AspNetCore.Authorization;

namespace WTCMSLive.WebSite.Core.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ServerConfController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly string _configPath;
        private readonly IServiceManagementService _serviceManagementService;
        private readonly IServiceLogService _serviceLogService;
        private readonly ILogger<ServerConfController> _logger;

        public ServerConfController(
            IConfiguration configuration,
            IServiceManagementService serviceManagementService,
            IServiceLogService serviceLogService,
            ILogger<ServerConfController> logger)
        {
            _configuration = configuration;
            _serviceManagementService = serviceManagementService;
            _serviceLogService = serviceLogService;
            _logger = logger;
            _configPath = _configuration.GetValue<string>("serverXMConfigPath");

            // 确保配置路径存在
            if (!string.IsNullOrEmpty(_configPath) && !Directory.Exists(_configPath))
            {
                Directory.CreateDirectory(_configPath);
            }
        }

        /// <summary>
        /// 获取配置文件列表
        /// </summary>
        /// <returns>配置文件列表</returns>
        [HttpGet("GetConfigFileList")]
        public List<ConfigFileInfoDTO> GetConfigFileList()
        {
            try
            {
                if (string.IsNullOrEmpty(_configPath) || !Directory.Exists(_configPath))
                {
                    return new List<ConfigFileInfoDTO>();
                }

                var configFiles = new List<ConfigFileInfoDTO>();
                var allowedExtensions = new[] { ".xml", ".config",".json" };

                var files = Directory.GetFiles(_configPath)
                    .Where(file => allowedExtensions.Contains(Path.GetExtension(file).ToLower()))
                    .OrderBy(file => Path.GetFileName(file));

                foreach (var filePath in files)
                {
                    var fileInfo = new FileInfo(filePath);
                    configFiles.Add(new ConfigFileInfoDTO
                    {
                        FileName = fileInfo.Name,
                        FilePath = filePath,
                        Extension = fileInfo.Extension,
                        FileSize = fileInfo.Length,
                        LastModified = fileInfo.LastWriteTime
                    });
                }

                return configFiles;
            }
            catch (Exception ex)
            {
                // 记录错误日志
                CMSFramework.Logger.Logger.LogErrorMessage("[GetConfigFileList]获取配置文件列表失败", ex);
                return new List<ConfigFileInfoDTO>();
            }
        }

        /// <summary>
        /// 获取配置文件内容
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <returns>配置文件内容</returns>
        [HttpGet("GetConfigFileContent")]
        public ConfigFileContentDTO GetConfigFileContent(string fileName)
        {
            try
            {
                if (string.IsNullOrEmpty(fileName))
                {
                    return null;
                }

                var filePath = GetSafeFilePath(fileName);
                if (string.IsNullOrEmpty(filePath) || !System.IO.File.Exists(filePath))
                {
                    return null;
                }

                var fileInfo = new FileInfo(filePath);
                var content = System.IO.File.ReadAllText(filePath, Encoding.UTF8);

                return new ConfigFileContentDTO
                {
                    FileName = fileName,
                    Content = content,
                    Encoding = "UTF-8",
                    FileSize = fileInfo.Length,
                    LastModified = fileInfo.LastWriteTime
                };
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[GetConfigFileContent]获取配置文件内容失败: {fileName}", ex);
                return null;
            }
        }

        /// <summary>
        /// 保存配置文件内容
        /// </summary>
        /// <param name="request">保存请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("SaveConfigFile")]
        public IActionResult SaveConfigFile([FromBody] SaveConfigFileRequestDTO request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.FileName) || string.IsNullOrEmpty(request.Content))
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                var filePath = GetSafeFilePath(request.FileName);
                if (string.IsNullOrEmpty(filePath))
                {
                    return Ok(ApiResponse<string>.Error("无效的文件名"));
                }

                // 确保目录存在
                var directory = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 备份原文件（如果存在）
                if (System.IO.File.Exists(filePath))
                {
                    var backupPath = filePath + ".backup." + DateTime.Now.ToString("yyyyMMddHHmmss");
                    System.IO.File.Copy(filePath, backupPath);
                }

                // 保存文件
                var encoding = GetEncodingFromString(request.Encoding);
                System.IO.File.WriteAllText(filePath, request.Content, encoding);

                return Ok(ApiResponse<string>.Success("配置文件保存成功"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[SaveConfigFile]保存配置文件失败: {request?.FileName}", ex);
                return Ok(ApiResponse<string>.Error($"保存配置文件失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 下载配置文件
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <returns>文件下载</returns>
        [HttpGet("DownloadConfigFile")]
        public IActionResult DownloadConfigFile(string fileName)
        {
            try
            {
                if (string.IsNullOrEmpty(fileName))
                {
                    return BadRequest("文件名不能为空");
                }

                var filePath = GetSafeFilePath(fileName);
                if (string.IsNullOrEmpty(filePath) || !System.IO.File.Exists(filePath))
                {
                    return NotFound("文件不存在");
                }

                var fileBytes = System.IO.File.ReadAllBytes(filePath);
                var contentType = GetContentType(fileName);

                return File(fileBytes, contentType, fileName);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[DownloadConfigFile]下载配置文件失败: {fileName}", ex);
                return StatusCode(500, "下载文件失败");
            }
        }

        /// <summary>
        /// 上传配置文件
        /// </summary>
        /// <param name="file">上传的文件</param>
        /// <returns>上传结果</returns>
        [HttpPost("UploadConfigFile")]
        public async Task<IActionResult> UploadConfigFile(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return Ok(ApiResponse<UploadConfigFileResponseDTO>.Error("请选择要上传的文件"));
                }

                var fileName = file.FileName;
                var extension = Path.GetExtension(fileName).ToLower();

                // 验证文件扩展名
                if (extension != ".xml" && extension != ".config" && extension != ".json" && extension != ".so" && extension != ".dll")
                {
                    return Ok(ApiResponse<UploadConfigFileResponseDTO>.Error("只支持上传 .xml .config .so .dll和.json 文件"));
                }

                var filePath = GetSafeFilePath(fileName);
                if (string.IsNullOrEmpty(filePath))
                {
                    return Ok(ApiResponse<UploadConfigFileResponseDTO>.Error("无效的文件名"));
                }

                // 检查文件是否已存在
                bool isReplaced = System.IO.File.Exists(filePath);

                // 备份原文件（如果存在）
                if (isReplaced)
                {
                    var backupPath = filePath + ".backup." + DateTime.Now.ToString("yyyyMMddHHmmss");
                    System.IO.File.Copy(filePath, backupPath);
                }

                // 确保目录存在
                var directory = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 保存上传的文件
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                var response = new UploadConfigFileResponseDTO
                {
                    Success = true,
                    Message = isReplaced ? "文件上传成功，已替换原文件" : "文件上传成功",
                    FileName = fileName,
                    FileSize = file.Length,
                    IsReplaced = isReplaced
                };

                return Ok(ApiResponse<UploadConfigFileResponseDTO>.Success(response, response.Message));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[UploadConfigFile]上传配置文件失败: {file?.FileName}", ex);
                var errorResponse = new UploadConfigFileResponseDTO
                {
                    Success = false,
                    Message = $"上传文件失败: {ex.Message}",
                    FileName = file?.FileName ?? "",
                    FileSize = file?.Length ?? 0,
                    IsReplaced = false
                };
                return Ok(ApiResponse<UploadConfigFileResponseDTO>.Error(errorResponse.Message));
            }
        }

        #region 私有辅助方法

        /// <summary>
        /// 获取安全的文件路径，防止路径遍历攻击
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <returns>安全的文件路径</returns>
        private string GetSafeFilePath(string fileName)
        {
            try
            {
                if (string.IsNullOrEmpty(fileName) || string.IsNullOrEmpty(_configPath))
                {
                    return null;
                }

                // 移除路径中的危险字符
                fileName = Path.GetFileName(fileName);

                // 验证文件扩展名
                var extension = Path.GetExtension(fileName).ToLower();
                if (extension != ".xml" && extension != ".config" && extension != ".json" && extension != ".so" && extension != ".dll")
                {
                    return null;
                }

                // 构建完整路径
                var fullPath = Path.Combine(_configPath, fileName);

                // 确保路径在配置目录内（防止路径遍历攻击）
                var normalizedConfigPath = Path.GetFullPath(_configPath);
                var normalizedFilePath = Path.GetFullPath(fullPath);

                if (!normalizedFilePath.StartsWith(normalizedConfigPath + Path.DirectorySeparatorChar) &&
                    !normalizedFilePath.Equals(normalizedConfigPath))
                {
                    return null;
                }

                return fullPath;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 根据文件扩展名获取Content-Type
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <returns>Content-Type</returns>
        private string GetContentType(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLower();
            return extension switch
            {
                ".xml" => "application/xml",
                ".config" => "application/xml",
                ".json" => "application/json",
                _ => "application/octet-stream"
            };
        }

        /// <summary>
        /// 从字符串获取编码
        /// </summary>
        /// <param name="encodingName">编码名称</param>
        /// <returns>编码对象</returns>
        private Encoding GetEncodingFromString(string encodingName)
        {
            if (string.IsNullOrEmpty(encodingName))
            {
                return Encoding.UTF8;
            }

            try
            {
                return encodingName.ToUpper() switch
                {
                    "UTF-8" or "UTF8" => Encoding.UTF8,
                    "UTF-16" or "UTF16" => Encoding.Unicode,
                    "ASCII" => Encoding.ASCII,
                    "GBK" or "GB2312" => Encoding.GetEncoding("GBK"),
                    _ => Encoding.UTF8
                };
            }
            catch
            {
                return Encoding.UTF8;
            }
        }

        #endregion

        #region 服务管理接口

        /// <summary>
        /// 获取所有服务配置
        /// </summary>
        /// <returns>服务配置列表</returns>
        [HttpGet("GetServiceConfigs")]
        public async Task<List<ServiceConfigDTO>> GetServiceConfigs()
        {
            try
            {
                return await _serviceManagementService.GetServiceConfigsAsync();
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[GetServiceConfigs]获取服务配置失败", ex);
                return new List<ServiceConfigDTO>();
            }
        }

        /// <summary>
        /// 获取单个服务配置
        /// </summary>
        /// <param name="serviceId">服务ID</param>
        /// <returns>服务配置</returns>
        [HttpGet("GetServiceConfig")]
        public async Task<ServiceConfigDTO> GetServiceConfig(string serviceId)
        {
            try
            {
                if (string.IsNullOrEmpty(serviceId))
                {
                    return null;
                }

                return await _serviceManagementService.GetServiceConfigAsync(serviceId);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[GetServiceConfig]获取服务配置失败: {serviceId}", ex);
                return null;
            }
        }

        /// <summary>
        /// 保存服务配置
        /// </summary>
        /// <param name="config">服务配置</param>
        /// <returns>操作结果</returns>
        [HttpPost("SaveServiceConfig")]
        public async Task<IActionResult> SaveServiceConfig([FromBody] ServiceConfigDTO config)
        {
            try
            {
                if (config == null)
                {
                    return Ok(ApiResponse<string>.Error("服务配置不能为空"));
                }

                if (string.IsNullOrEmpty(config.ServiceName))
                {
                    return Ok(ApiResponse<string>.Error("服务名称不能为空"));
                }

                if (string.IsNullOrEmpty(config.SystemServiceName))
                {
                    return Ok(ApiResponse<string>.Error("系统服务名称不能为空"));
                }

                var result = await _serviceManagementService.SaveServiceConfigAsync(config);
                if (result)
                {
                    return Ok(ApiResponse<string>.Success("服务配置保存成功"));
                }
                else
                {
                    return Ok(ApiResponse<string>.Error("服务配置保存失败"));
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[SaveServiceConfig]保存服务配置失败: {config?.ServiceName}", ex);
                return Ok(ApiResponse<string>.Error($"保存服务配置失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 删除服务配置
        /// </summary>
        /// <param name="request">删除请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("DeleteServiceConfig")]
        public async Task<IActionResult> DeleteServiceConfig([FromBody] ServiceOperationRequestDTO request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.ServiceId))
                {
                    return Ok(ApiResponse<string>.Error("服务ID不能为空"));
                }

                var result = await _serviceManagementService.DeleteServiceConfigAsync(request.ServiceId);
                if (result)
                {
                    return Ok(ApiResponse<string>.Success("服务配置删除成功"));
                }
                else
                {
                    return Ok(ApiResponse<string>.Error("服务配置删除失败"));
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[DeleteServiceConfig]删除服务配置失败: {request?.ServiceId}", ex);
                return Ok(ApiResponse<string>.Error($"删除服务配置失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 获取所有服务状态
        /// </summary>
        /// <returns>服务状态列表</returns>
        [HttpGet("GetServiceStatuses")]
        public async Task<List<ServiceStatusDTO>> GetServiceStatuses()
        {
            try
            {
                return await _serviceManagementService.GetServiceStatusesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取服务状态失败");
                return new List<ServiceStatusDTO>();
            }
        }

        /// <summary>
        /// 获取单个服务状态
        /// </summary>
        /// <param name="serviceId">服务ID</param>
        /// <returns>服务状态</returns>
        [HttpGet("GetServiceStatus")]
        public async Task<ServiceStatusDTO> GetServiceStatus(string serviceId)
        {
            try
            {
                if (string.IsNullOrEmpty(serviceId))
                {
                    return null;
                }

                return await _serviceManagementService.GetServiceStatusAsync(serviceId);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[GetServiceStatus]获取服务状态失败: {serviceId}", ex);
                return null;
            }
        }

        /// <summary>
        /// 启动服务
        /// </summary>
        /// <param name="request">操作请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("StartService")]
        public async Task<IActionResult> StartService([FromBody] ServiceOperationRequestDTO request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.ServiceId))
                {
                    return Ok(ApiResponse<string>.Error("服务ID不能为空"));
                }

                var result = await _serviceManagementService.StartServiceAsync(request.ServiceId);
                if (result)
                {
                    return Ok(ApiResponse<string>.Success("服务启动成功"));
                }
                else
                {
                    return Ok(ApiResponse<string>.Error("服务启动失败"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动服务失败: {ServiceId}", request?.ServiceId);
                return Ok(ApiResponse<string>.Error($"启动服务失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 停止服务
        /// </summary>
        /// <param name="request">操作请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("StopService")]
        public async Task<IActionResult> StopService([FromBody] ServiceOperationRequestDTO request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.ServiceId))
                {
                    return Ok(ApiResponse<string>.Error("服务ID不能为空"));
                }

                var result = await _serviceManagementService.StopServiceAsync(request.ServiceId);
                if (result)
                {
                    return Ok(ApiResponse<string>.Success("服务停止成功"));
                }
                else
                {
                    return Ok(ApiResponse<string>.Error("服务停止失败"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止服务失败: {ServiceId}", request?.ServiceId);
                return Ok(ApiResponse<string>.Error($"停止服务失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 重启服务
        /// </summary>
        /// <param name="request">操作请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("RestartService")]
        public async Task<IActionResult> RestartService([FromBody] ServiceOperationRequestDTO request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.ServiceId))
                {
                    return Ok(ApiResponse<string>.Error("服务ID不能为空"));
                }

                var result = await _serviceManagementService.RestartServiceAsync(request.ServiceId);
                if (result)
                {
                    return Ok(ApiResponse<string>.Success("服务重启成功"));
                }
                else
                {
                    return Ok(ApiResponse<string>.Error("服务重启失败"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重启服务失败: {ServiceId}", request?.ServiceId);
                return Ok(ApiResponse<string>.Error($"重启服务失败: {ex.Message}"));
            }
        }

        #endregion

        #region 日志管理相关接口

        /// <summary>
        /// 获取服务日志文件列表
        /// </summary>
        /// <param name="serviceId">服务ID</param>
        /// <param name="logType">日志类型 (debuggerlog/errorlog)</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>日志文件列表</returns>
        [HttpGet("GetLogFiles")]
        public async Task<IActionResult> GetLogFiles(string serviceId, string logType, DateTime? startTime = null, DateTime? endTime = null)
        {
            try
            {
                if (string.IsNullOrEmpty(serviceId))
                {
                    return Ok(ApiResponse<List<LogFileInfoDTO>>.Error("服务ID不能为空"));
                }

                if (string.IsNullOrEmpty(logType))
                {
                    return Ok(ApiResponse<List<LogFileInfoDTO>>.Error("日志类型不能为空"));
                }

                if (!IsValidLogType(logType))
                {
                    return Ok(ApiResponse<List<LogFileInfoDTO>>.Error("不支持的日志类型，支持的类型: debuggerlog, errorlog"));
                }

                var logFiles = await _serviceLogService.GetLogFilesAsync(serviceId, logType, startTime, endTime);
                return Ok(logFiles);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取日志文件列表失败: {ServiceId}, {LogType}", serviceId, logType);
                return Ok(ApiResponse<List<LogFileInfoDTO>>.Error($"获取日志文件列表失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 获取日志文件内容
        /// </summary>
        /// <param name="serviceId">服务ID</param>
        /// <param name="logType">日志类型</param>
        /// <param name="fileName">文件名</param>
        /// <returns>日志内容</returns>
        [HttpGet("GetLogContent")]
        public async Task<IActionResult> GetLogContent(string serviceId, string logType, string fileName)
        {
            try
            {
                if (string.IsNullOrEmpty(serviceId))
                {
                    return Ok(ApiResponse<LogContentDTO>.Error("服务ID不能为空"));
                }

                if (string.IsNullOrEmpty(logType))
                {
                    return Ok(ApiResponse<LogContentDTO>.Error("日志类型不能为空"));
                }

                if (string.IsNullOrEmpty(fileName))
                {
                    return Ok(ApiResponse<LogContentDTO>.Error("文件名不能为空"));
                }

                if (!IsValidLogType(logType))
                {
                    return Ok(ApiResponse<LogContentDTO>.Error("不支持的日志类型，支持的类型: debuggerlog, errorlog"));
                }

                var logContent = await _serviceLogService.GetLogContentAsync(serviceId, logType, fileName);
                return Ok(logContent);
            }
            catch (FileNotFoundException)
            {
                return Ok(ApiResponse<LogContentDTO>.Error("日志文件不存在"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取日志内容失败: {ServiceId}, {LogType}, {FileName}", serviceId, logType, fileName);
                return Ok(ApiResponse<LogContentDTO>.Error($"获取日志内容失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 开始实时日志监控
        /// </summary>
        /// <param name="request">实时日志控制请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("StartRealTimeLog")]
        public async Task<IActionResult> StartRealTimeLog([FromBody] RealTimeLogControlRequestDTO request)
        {
            try
            {
                if (request == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                if (string.IsNullOrEmpty(request.ServiceId))
                {
                    return Ok(ApiResponse<string>.Error("服务ID不能为空"));
                }

                if (string.IsNullOrEmpty(request.LogType))
                {
                    return Ok(ApiResponse<string>.Error("日志类型不能为空"));
                }

                if (!IsValidLogType(request.LogType))
                {
                    return Ok(ApiResponse<string>.Error("不支持的日志类型，支持的类型: debuggerlog, errorlog"));
                }

                // 获取当前连接ID
                var connectionId = HttpContext.Request.Headers["ConnectionId"].FirstOrDefault() ?? "default";

                var result = await _serviceLogService.StartRealTimeLogAsync(request.ServiceId, request.LogType, connectionId);
                if (result)
                {
                    return Ok(ApiResponse<string>.Success("开始实时日志监控成功"));
                }
                else
                {
                    return Ok(ApiResponse<string>.Error("开始实时日志监控失败"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "开始实时日志监控失败: {ServiceId}, {LogType}", request?.ServiceId, request?.LogType);
                return Ok(ApiResponse<string>.Error($"开始实时日志监控失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 停止实时日志监控
        /// </summary>
        /// <param name="request">实时日志控制请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("StopRealTimeLog")]
        public async Task<IActionResult> StopRealTimeLog([FromBody] RealTimeLogControlRequestDTO request)
        {
            try
            {
                if (request == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                if (string.IsNullOrEmpty(request.ServiceId))
                {
                    return Ok(ApiResponse<string>.Error("服务ID不能为空"));
                }

                if (string.IsNullOrEmpty(request.LogType))
                {
                    return Ok(ApiResponse<string>.Error("日志类型不能为空"));
                }

                if (!IsValidLogType(request.LogType))
                {
                    return Ok(ApiResponse<string>.Error("不支持的日志类型，支持的类型: debuggerlog, errorlog"));
                }

                // 获取当前连接ID
                var connectionId = HttpContext.Request.Headers["ConnectionId"].FirstOrDefault() ?? "default";

                var result = await _serviceLogService.StopRealTimeLogAsync(request.ServiceId, request.LogType, connectionId);
                if (result)
                {
                    return Ok(ApiResponse<string>.Success("停止实时日志监控成功"));
                }
                else
                {
                    return Ok(ApiResponse<string>.Error("停止实时日志监控失败"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止实时日志监控失败: {ServiceId}, {LogType}", request?.ServiceId, request?.LogType);
                return Ok(ApiResponse<string>.Error($"停止实时日志监控失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 检查日志目录是否存在
        /// </summary>
        /// <param name="serviceId">服务ID</param>
        /// <param name="logType">日志类型</param>
        /// <returns>检查结果</returns>
        [HttpGet("CheckLogDirectory")]
        public async Task<IActionResult> CheckLogDirectory(string serviceId, string logType)
        {
            try
            {
                if (string.IsNullOrEmpty(serviceId))
                {
                    return Ok(ApiResponse<bool>.Error("服务ID不能为空"));
                }

                if (string.IsNullOrEmpty(logType))
                {
                    return Ok(ApiResponse<bool>.Error("日志类型不能为空"));
                }

                if (!IsValidLogType(logType))
                {
                    return Ok(ApiResponse<bool>.Error("不支持的日志类型，支持的类型: debuggerlog, errorlog"));
                }

                var exists = await _serviceLogService.CheckLogDirectoryExistsAsync(serviceId, logType);
                return Ok(ApiResponse<bool>.Success(exists, exists ? "日志目录存在" : "日志目录不存在"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查日志目录失败: {ServiceId}, {LogType}", serviceId, logType);
                return Ok(ApiResponse<bool>.Error($"检查日志目录失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 验证日志类型是否有效
        /// </summary>
        /// <param name="logType">日志类型</param>
        /// <returns>是否有效</returns>
        private bool IsValidLogType(string logType)
        {
            var validTypes = new[] { "debuggerlog", "errorlog" };
            return validTypes.Contains(logType.ToLowerInvariant());
        }

        #endregion
    }
}
