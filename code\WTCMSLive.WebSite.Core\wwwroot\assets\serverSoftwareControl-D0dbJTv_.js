import{W as Q}from"./table-C0LB8EJD.js";import{O as X}from"./index-ChYhAQZ_.js";import{r as a,y as Z,w as x,dD as ee,f as S,d as h,u as te,o as d,i as r,b as F,c as D,s as Y,g as oe,t as ne,m as s,z as M,a8 as ae}from"./index-DLKlSkMo.js";import{u as se}from"./serverManager-CviCQwq-.js";import{s as T}from"./useWebSocket-Br1zZvLi.js";import{_ as re}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{S as le}from"./tools-B0tc7tWY.js";import{M as ie}from"./index-BJVFa-Ub.js";import{B as ce}from"./index-bX4hVoaz.js";import"./ActionButton-DDquCBiw.js";import"./styleChecker-tOiS7TSB.js";import"./initDefaultProps-DBdyDbXv.js";import"./shallowequal-C1uyuyZ_.js";import"./index-W_N8ii3N.js";import"./index-DzeUeE5s.js";import"./index-C5qlGDhC.js";import"./index-pJrpCCGr.js";import"./index-Big_Av0Y.js";const ue={key:0},de=["onClick"],me=["onClick"],pe=["onClick"],ve={key:1},fe=["onClick"],ge=["onClick"],he={class:"logBox"},ye={__name:"serverSoftwareControl",setup(Ce){const l=se(),y=a(""),c=a(""),C=a(!1),B=a(),R=a({}),_=[{title:"选择时间",dataIndex:"time",inputType:"rangePicker",isrequired:!0,timeFormat:["YYYY-MM-DD","YYYY-MM-DD"],hasChangeEvent:!0},{title:"日志类型",dataIndex:"logType",inputType:"select",isrequired:!0,formItemWidth:140,hasChangeEvent:!0,selectOptions:[{value:"errorlog",label:"错误日志"},{value:"debuggerlog",label:"debug日志"}]},{title:"日志名称",dataIndex:"fileName",inputType:"select",isrequired:!0,formItemWidth:160}],m=a(),p=a(!1),o=Z({currentService:{},tableDatas:[],formList:_,historyLogFileContent:null}),i=a(""),k=a(""),N=[{title:"服务器名称",dataIndex:"serviceName",align:"center",columnWidth:150},{title:"类型",dataIndex:"systemServiceName",align:"center",columnWidth:150},{title:"服务器状态",dataIndex:"statusDescription",align:"center",columnWidth:150},{title:"服务器软件操作",dataIndex:"otherColumn",columnWidth:120,align:"center",mark:"1"},{title:"服务器软件日志",dataIndex:"otherColumn",align:"center",columnWidth:100,mark:"2"}],v=a(!1),W=te(),f=async e=>{C.value=!0;let t=await l.fetchGetServiceStatuses();o.tableDatas=t||[],C.value=!1};x(()=>W.params.id,e=>{f()},{immediate:!0});function V(e,t){let n=null;return function(...b){n&&clearTimeout(n),n=setTimeout(()=>{e.apply(this,b)},t)}}const H=V(async()=>{try{await ae();const e=document.querySelector(".logBox");e&&(e.scrollHeight>e.clientHeight?e.scrollTo({top:e.scrollHeight,behavior:"smooth"}):e.scrollTo({top:e.scrollHeight}))}catch(e){console.error("滚动到日志框底部时出错:",e)}},100);x(()=>i.value,e=>{H()});const I=()=>{v.value=!0},O=e=>{v.value=!1,o.formList=[],o.currentService={},m.value.resetFields(),c.value=="current"?L():o.historyLogFileContent=null,i.value="",c.value=""},U=async e=>{if(e.status==3)return;let t=await l.fetchStartService({serviceId:e.serviceId});t&&t.code==1?(s.success("启动成功"),f()):s.error(t.msg)},$=async e=>{if(e.status==1)return;let t=await l.fetchStopService({serviceId:e.serviceId});t&&t.code==1?(s.success("停止成功"),f()):s.error(t.msg)},q=async e=>{let t=await l.fetchRestartService({serviceId:e.serviceId});t&&t.code==1?(s.success("重启成功"),f()):s.error(t.msg)},E=e=>{if(e&&(e.dataIndex&&e.dataIndex=="logType"||e.dataIndex=="time")){let t=m.value.getFieldsValue();t&&t.time&&t.time.length>1&&t.logType&&j(t)}},j=async e=>{let t=await l.fetchGetLogFiles({serviceId:o.currentService.serviceId,logType:e.logType,startTime:M(e.time[0]).format("YYYY-MM-DD"),endTime:M(e.time[1]).format("YYYY-MM-DD")});o.formList[2].selectOptions=t,m.value.setFieldValue({fileName:""})},G=e=>{o.currentService=e,y.value="查看服务器日志",c.value="history",I(),o.formList=_},P=()=>{if(!o.historyLogFileContent)return;const e=new Blob([o.historyLogFileContent.content],{type:"text/plain"}),t=URL.createObjectURL(e),n=document.createElement("a");n.href=t,n.download=o.historyLogFileContent.fileName,document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(t),s.success("下载成功")},L=async()=>{p.value&&(await T.stopConnection(),p.value=!1)},z=async e=>{if(c.value=="current"){if(k.value==e.logType)return;k.value=e.logType;let n=await l.fetchStartRealTimeLog({ConnectionId:p.value},{serviceId:o.currentService.serviceId,LogType:e.logType});n&&n.code==1?s.success("开启实时日志服务成功"):s.error(n.msg);return}let t=await l.fetchGetLogContent({serviceId:o.currentService.serviceId,logType:e.logType,fileName:e.fileName});t&&t.content?(i.value=t.content,o.historyLogFileContent=t):(o.historyLogFileContent=null,i.value="")},A=async()=>{const e=await T.startConnection("/Hubs/ServerPerformanceHub");p.value=e,e?T.onReceiveMessage("ReceiveServiceLog",t=>{i.value=i.value+(t&&t.content?t.content:"")+`
`}):s.error("连接失败")},J=async e=>{o.currentService=e,y.value="查看服务器实时日志",c.value="current",o.formList=[_[1]],A(),I()};return ee(()=>{L()}),(e,t)=>{const n=ce,b=ie,K=le;return d(),S(K,{spinning:C.value,size:"large"},{default:h(()=>[r("div",null,[F(Q,{tableTitle:"服务器软件列表","table-key":"0","table-columns":N,"table-operate":[],"record-key":"serviceId","table-datas":o.tableDatas,noBatchApply:!0,actionCloumnProps:{width:170,align:"center"}},{otherColumn:h(({column:w,record:u,text:be})=>[w.mark=="1"?(d(),D("div",ue,[r("span",{onClick:g=>U(u),class:"btnSpan"},"运行",8,de),r("span",{onClick:g=>$(u),class:"btnSpan"},"停止",8,me),r("span",{onClick:g=>q(u),class:"btnSpan"},"重启",8,pe)])):w.mark=="2"?(d(),D("div",ve,[r("span",{onClick:g=>G(u),class:"btnSpan"},"历史日志",8,fe),r("span",{onClick:g=>J(u),class:"btnSpan"},"实时日志",8,ge)])):Y("",!0)]),_:1},8,["table-datas"])]),F(b,{maskClosable:!1,width:1200,open:v.value,title:y.value,footer:"",onCancel:O},{default:h(()=>[(d(),S(X,{key:v.value,ref_key:"modalFormRef",ref:m,formlayout:"inline",titleCol:o.formList,form:B.value,initFormData:R.value,onChange:E,onSubmit:z},null,8,["titleCol","form","initFormData"])),t[1]||(t[1]=r("div",null,null,-1)),c.value=="history"?(d(),S(n,{key:0,class:"downloadlog",type:"primary",onClick:P},{default:h(()=>t[0]||(t[0]=[oe("下载日志",-1)])),_:1,__:[0]})):Y("",!0),r("div",he,ne(i.value),1)]),_:1,__:[1]},8,["open","title"])]),_:1},8,["spinning"])}}},Ue=re(ye,[["__scopeId","data-v-9a9ffd25"]]);export{Ue as default};
