import{d as l,e as r,a as s}from"./tools-B0tc7tWY.js";import{C as o,V as n,W as d,X as u,Y as c}from"./index-DLKlSkMo.js";const p="YYYY-MM-DD",v=[{value:"河北",label:"河北"},{value:"山西",label:"山西"},{value:"黑龙江",label:"黑龙江"},{value:"吉林",label:"吉林"},{value:"辽宁",label:"辽宁"},{value:"江苏",label:"江苏"},{value:"浙江",label:"浙江"},{value:"安徽",label:"安徽"},{value:"福建",label:"福建"},{value:"江西",label:"江西"},{value:"山东",label:"山东"},{value:"河南",label:"河南"},{value:"湖北",label:"湖北"},{value:"湖南",label:"湖南"},{value:"广东",label:"广东"},{value:"海南",label:"海南"},{value:"四川",label:"四川"},{value:"贵州",label:"贵州"},{value:"云南",label:"云南"},{value:"陕西",label:"陕西"},{value:"甘肃",label:"甘肃"},{value:"青海",label:"青海"},{value:"台湾",label:"台湾"},{value:"内蒙古",label:"内蒙古"},{value:"广西",label:"广西"},{value:"西藏",label:"西藏"},{value:"宁夏",label:"宁夏"},{value:"新疆",label:"新疆"},{value:"北京",label:"北京"},{value:"天津",label:"天津"},{value:"上海",label:"上海"},{value:"重庆",label:"重庆"},{value:"香港",label:"香港"},{value:"澳门",label:"澳门"}];let a=320;const b=(e={isEdit:!1})=>[{title:"id",dataIndex:"windParkID",formItemWidth:a,isdisplay:!1},{title:"集团公司",isdisplay:e&&!e.isEdit,dataIndex:"windParkGroupName",formItemWidth:a,inputType:"select",selectOptions:[],isrequired:e&&!e.isEdit,isdisplay:e&&!e.isEdit},{title:"厂站名称",dataIndex:"windParkName",formItemWidth:a,isrequired:!0},{title:"厂站编号",dataIndex:"windParkCode",formItemWidth:a,isrequired:e&&!e.isEdit,isdisplay:e&&!e.isEdit,validateRules:[{pattern:/^\d{1,3}$/,message:"厂站编号是1至3位的数字"}]},{title:"投运日期",dataIndex:"operationalDate",formItemWidth:a,inputType:"datepicker",isrequired:!0,timeFormat:p},{title:"联系人",dataIndex:"contactMan",formItemWidth:a,isrequired:!0},{title:"联系人电话",dataIndex:"contactTel",formItemWidth:a,validateRules:l({title:"联系人电话",type:"phone",required:!0})},{title:"国家",dataIndex:"country",formItemWidth:a,inputType:"select",selectOptions:[{label:"中国",value:"中国"}],validateRules:l({title:"国家",required:!0})},{title:"省份 ",dataIndex:"area",formItemWidth:a,noColon:!0,inputType:"select",selectOptions:v,validateRules:l({title:"省份",required:!0})},{title:"厂站地址",dataIndex:"address",formItemWidth:a,isrequired:!0},{title:"经纬度",dataIndex:"location",formItemWidth:a,isrequired:!0,validateRules:[{pattern:/^(\-?\d+(\.\d+)?),\s*(\-?\d+(\.\d+)?)$/,message:"格式不正确！请输入两个数字并用英文逗号隔开!"}]},{title:"邮编",dataIndex:"postCode",formItemWidth:a,isrequired:!0,validateRules:l({title:"邮编",type:"postCode"})},{title:"概况",dataIndex:"description",formItemWidth:a}];b();const k=o("configRoot",{state:()=>({parkList:[],parkOptions:[],editWindpark:{},groupCompanyList:[]}),actions:{async fetchParkList(){try{const e=await c();return this.parkList=e,this.parkOptions=s(e,{label:"windParkName",value:"windParkID"},{nother:!0}),e}catch(e){throw console.error("获取厂站失败:",e),e}},async fetchEditWindparkInformation(e={}){try{const t=await u(e);return t&&t.length>0,t}catch(t){throw console.error("编辑厂站失败:",t),t}},async fetchGroupCompanyList(e={}){try{const t=await d(e);let i=r(t,!0);return this.groupCompanyList=i,i}catch(t){throw console.error("获取集团公司失败:",t),t}},async fetchDeletetPark(e={}){try{return await n(e)}catch(t){throw console.error("删除厂站失败:",t),t}}}});export{b as g,k as u};
